import React, { useState } from 'react';
import { X, Save, TestTube } from 'lucide-react';

const SettingsDialog = ({ isOpen, onClose, onSave, onTest, initialConfig }) => {
  const [config, setConfig] = useState(initialConfig || {
    api_key: '',
    base_url: 'https://aistudio.baidu.com/llm/lmapi/v3',
    model: 'ernie-4.5-turbo-vl-preview'
  });
  
  const [testResult, setTestResult] = useState(null);
  const [isTesting, setIsTesting] = useState(false);

  const handleTest = async () => {
    setIsTesting(true);
    setTestResult(null);
    try {
      const result = await onTest(config);
      setTestResult({ success: true, message: '连接测试成功！' });
    } catch (error) {
      setTestResult({ 
        success: false, 
        message: `连接测试失败: ${error.message || '未知错误'}` 
      });
    } finally {
      setIsTesting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-96 max-w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">AI服务器设置</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">API Key</label>
            <input
              type="text"
              value={config.api_key}
              onChange={(e) => setConfig({...config, api_key: e.target.value})}
              className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入API Key"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">基础URL</label>
            <input
              type="text"
              value={config.base_url}
              onChange={(e) => setConfig({...config, base_url: e.target.value})}
              className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入基础URL"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">模型名称</label>
            <input
              type="text"
              value={config.model}
              onChange={(e) => setConfig({...config, model: e.target.value})}
              className="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入模型名称"
            />
          </div>

          {testResult && (
            <div className={`p-4 rounded-lg ${testResult.success ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
              <div className="font-medium">{testResult.success ? '成功' : '错误'}</div>
              <div className="text-sm mt-1">{testResult.message}</div>
            </div>
          )}

          <div className="flex space-x-2">
            <button
              onClick={handleTest}
              disabled={isTesting}
              className="flex-1 flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isTesting ? (
                <span>测试中...</span>
              ) : (
                <>
                  <TestTube size={16} className="mr-2" />
                  测试连接
                </>
              )}
            </button>
            
            <button
              onClick={() => onSave(config)}
              className="flex-1 flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <Save size={16} className="mr-2" />
              保存
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

// 保留SettingsDialog本身内容，不包含RoleStaffConfig组件实现。
export default SettingsDialog;
