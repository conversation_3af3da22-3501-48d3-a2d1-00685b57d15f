import React from 'react'
import { 
  <PERSON><PERSON><PERSON>, 
  FileText, 
  LibraryBig, 
  BarChart2, 
  CheckSquare, 
  Coins, 
  Receipt, 
  PiggyBank, 
  Users, 
  DollarSign,
  Settings,
  Table
} from 'lucide-react'

const FunctionBar = ({ activeFeature, onFeatureSelect, features, onShowSettings }) => {
  const icons = {
    agent: <BookOpen size={20} />,
    voucher: <FileText size={20} />,
    bookkeeping: <LibraryBig size={20} />,
    report: <BarChart2 size={20} />,
    settlement: <CheckSquare size={20} />,
    asset: <Coins size={20} />,
    invoice: <Receipt size={20} />,
    cashier: <PiggyBank size={20} />,
    salary: <Users size={20} />,
    tax: <DollarSign size={20} />,
    subject: <Table size={20} />,
    roleStaff: <Users size={20} />,
  }

  return (
    <div className="w-16 bg-gray-100 border-r border-gray-200 flex flex-col h-full">
      <div className="flex-1">
        {Object.entries(features).map(([key, feature]) => (
          <button
            key={key}
            onClick={() => onFeatureSelect(key)}
            className={`w-full p-3 flex flex-col items-center justify-center hover:bg-gray-200 transition-colors
              ${activeFeature === key ? 'bg-gray-200 text-blue-600' : 'text-gray-600'}`}
          >
            {icons[key]}
            <span className="text-[10px] mt-1">{feature.name}</span>
          </button>
        ))}
      </div>
      <div className="border-t border-gray-200">
        <button
          onClick={onShowSettings}
          className="w-full p-3 flex flex-col items-center justify-center hover:bg-gray-200 transition-colors text-gray-600"
        >
          <Settings size={20} />
          <span className="text-[10px] mt-1">设置</span>
        </button>
      </div>
    </div>
  )
}

export default FunctionBar 