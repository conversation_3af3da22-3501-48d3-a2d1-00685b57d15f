import React, { useEffect, useState, useRef } from 'react';

const Asset = () => {
  const [assets, setAssets] = useState([]);
  const [editAsset, setEditAsset] = useState(null);
  const [form, setForm] = useState({ code: '', name: '', category: '', original_value: '', net_value: '', purchase_date: '', useful_life: '', status: '', remark: '' });
  const fileInputRef = useRef();

  // 加载资产数据
  const fetchAssets = async () => {
    const res = await fetch('http://localhost:8000/assets');
    const data = await res.json();
    setAssets(data.assets || []);
  };

  useEffect(() => {
    fetchAssets();
  }, []);

  // 导出资产
  const handleExport = () => {
    window.open('http://localhost:8000/assets/export', '_blank');
  };

  // 导入资产
  const handleImport = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    const formData = new FormData();
    formData.append('file', file);
    await fetch('http://localhost:8000/assets/import', {
      method: 'POST',
      body: formData,
    });
    fetchAssets();
    fileInputRef.current.value = '';
  };

  // 删除资产
  const handleDelete = async (code) => {
    if (!window.confirm('确定要删除该资产吗？')) return;
    await fetch(`http://localhost:8000/assets/${code}`, { method: 'DELETE' });
    fetchAssets();
  };

  // 打开编辑弹窗
  const openEdit = (asset) => {
    setEditAsset(asset);
    setForm(asset);
  };

  // 关闭编辑弹窗
  const closeEdit = () => {
    setEditAsset(null);
  };

  // 提交编辑
  const handleEditSubmit = async (e) => {
    e.preventDefault();
    await fetch(`http://localhost:8000/assets/${form.code}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...form,
        original_value: parseFloat(form.original_value),
        net_value: parseFloat(form.net_value),
        useful_life: parseInt(form.useful_life, 10),
      }),
    });
    closeEdit();
    fetchAssets();
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-semibold">资产管理</h2>
        <div className="space-x-2">
          <input
            type="file"
            accept=".csv"
            ref={fileInputRef}
            style={{ display: 'none' }}
            onChange={handleImport}
          />
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={() => fileInputRef.current.click()}
          >
            导入
          </button>
          <button className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600" onClick={handleExport}>导出</button>
        </div>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full bg-white rounded shadow">
          <thead>
            <tr>
              <th className="px-4 py-2 border">编号</th>
              <th className="px-4 py-2 border">名称</th>
              <th className="px-4 py-2 border">类别</th>
              <th className="px-4 py-2 border">原值</th>
              <th className="px-4 py-2 border">净值</th>
              <th className="px-4 py-2 border">购置日期</th>
              <th className="px-4 py-2 border">使用年限</th>
              <th className="px-4 py-2 border">状态</th>
              <th className="px-4 py-2 border">备注</th>
              <th className="px-4 py-2 border">操作</th>
            </tr>
          </thead>
          <tbody>
            {assets.length === 0 ? (
              <tr><td colSpan={10} className="text-center py-8 text-gray-400">暂无数据</td></tr>
            ) : assets.map((a, idx) => (
              <tr key={a.code}>
                <td className="px-4 py-2 border">{a.code}</td>
                <td className="px-4 py-2 border">{a.name}</td>
                <td className="px-4 py-2 border">{a.category}</td>
                <td className="px-4 py-2 border text-right">{a.original_value?.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}</td>
                <td className="px-4 py-2 border text-right">{a.net_value?.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}</td>
                <td className="px-4 py-2 border">{a.purchase_date}</td>
                <td className="px-4 py-2 border text-right">{a.useful_life}</td>
                <td className="px-4 py-2 border">{a.status}</td>
                <td className="px-4 py-2 border">{a.remark}</td>
                <td className="px-4 py-2 border">
                  <button className="text-blue-500 hover:underline" onClick={() => openEdit(a)}>编辑</button>
                  <button className="text-red-500 hover:underline ml-2" onClick={() => handleDelete(a.code)}>删除</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {/* 编辑弹窗 */}
      {editAsset && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <form className="bg-white p-6 rounded shadow w-96" onSubmit={handleEditSubmit}>
            <h3 className="text-lg font-semibold mb-4">编辑资产</h3>
            <div className="mb-3">
              <label className="block mb-1">编号</label>
              <input className="w-full border rounded px-2 py-1" value={form.code} disabled />
            </div>
            <div className="mb-3">
              <label className="block mb-1">名称</label>
              <input className="w-full border rounded px-2 py-1" value={form.name} onChange={e => setForm(f => ({ ...f, name: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">类别</label>
              <input className="w-full border rounded px-2 py-1" value={form.category} onChange={e => setForm(f => ({ ...f, category: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">原值</label>
              <input className="w-full border rounded px-2 py-1" type="number" value={form.original_value} onChange={e => setForm(f => ({ ...f, original_value: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">净值</label>
              <input className="w-full border rounded px-2 py-1" type="number" value={form.net_value} onChange={e => setForm(f => ({ ...f, net_value: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">购置日期</label>
              <input className="w-full border rounded px-2 py-1" type="date" value={form.purchase_date} onChange={e => setForm(f => ({ ...f, purchase_date: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">使用年限</label>
              <input className="w-full border rounded px-2 py-1" type="number" value={form.useful_life} onChange={e => setForm(f => ({ ...f, useful_life: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">状态</label>
              <input className="w-full border rounded px-2 py-1" value={form.status} onChange={e => setForm(f => ({ ...f, status: e.target.value }))} required />
            </div>
            <div className="mb-3">
              <label className="block mb-1">备注</label>
              <input className="w-full border rounded px-2 py-1" value={form.remark} onChange={e => setForm(f => ({ ...f, remark: e.target.value }))} />
            </div>
            <div className="flex justify-end space-x-2 mt-4">
              <button type="button" className="px-4 py-2 bg-gray-300 rounded" onClick={closeEdit}>取消</button>
              <button type="submit" className="px-4 py-2 bg-blue-600 text-white rounded">保存</button>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default Asset;
