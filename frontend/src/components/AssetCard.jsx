import React, { useState } from 'react';

const AssetCard = (props) => {
  const { onConfirm = () => {}, editable = true, ...rest } = props;
  console.log('AssetCard props', props);
  const [form, setForm] = useState(rest || {
    资产编码: '',
    资产名称: '',
    类别: '',
    原值: '',
    净值: '',
    购置日期: '',
    使用年限: '',
    状态: '',
    备注: ''
  });
  const [isEditing, setIsEditing] = useState(editable);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleConfirm = () => {
    if (onConfirm) onConfirm(form);
    setIsEditing(false);
  };

  return (
    <div className="bg-white rounded-lg shadow p-4 border border-gray-200 mb-2 max-w-full" style={{maxHeight: '340px', overflowY: 'auto'}}>
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-lg font-semibold">资产卡片</h3>
        {isEditing ? null : (
          <button className="text-blue-500 text-sm" onClick={() => setIsEditing(true)}>编辑</button>
        )}
      </div>
      <div className="space-y-2">
        <div>
          <label className="block text-sm text-gray-600">资产编码</label>
          <input name="资产编码" value={form.资产编码} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">资产名称</label>
          <input name="资产名称" value={form.资产名称} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">类别</label>
          <input name="类别" value={form.类别} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">原值</label>
          <input name="原值" value={form.原值} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">净值</label>
          <input name="净值" value={form.净值} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">购置日期</label>
          <input name="购置日期" value={form.购置日期} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">使用年限</label>
          <input name="使用年限" value={form.使用年限} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">状态</label>
          <input name="状态" value={form.状态} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">备注</label>
          <input name="备注" value={form.备注} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
      </div>
      {isEditing && (
        <div className="mt-4 flex justify-end">
          <button className="bg-blue-500 text-white px-4 py-1 rounded" onClick={handleConfirm}>确认</button>
        </div>
      )}
    </div>
  );
};

export default AssetCard; 