import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, Plus, Minus, Calendar, Paperclip } from 'lucide-react';

// 金额单元格组件
const AmountCell = ({ amount, className = "" }) => {
  if (!amount && amount !== 0) {
    // 空单元格也需要保持相同的格式
    return (
      <td className={`${className} relative p-0`}>
        <div className="grid grid-cols-12 h-full">
          {[...Array(12)].map((_, i) => (
            <div key={i} className={`h-8 flex items-center justify-center ${
              i < 11 ? `border-r ${
                // 整数部分每三位加深分隔线，不包括角分
                i === 3 || i === 6 
                  ? 'border-gray-400 border-r-2' 
                  : i === 9 
                    ? 'border-gray-400 border-r-2'  // 元位加深
                    : 'border-gray-300'
              }` : ''
            }`}>&nbsp;</div>
          ))}
        </div>
      </td>
    );
  }
  
  // 将数字转换为固定的格式（2位小数）
  const formattedAmount = amount.toFixed(2);
  // 分割成整数和小数部分
  const [integerPart, decimalPart] = formattedAmount.split('.');
  // 补齐整数部分到10位
  const paddedInteger = integerPart.padStart(10, ' ');
  
  // 将整数部分分割成单位
  const digits = paddedInteger.split('').concat(decimalPart.split(''));

  return (
    <td className={`${className} relative p-0`}>
      <div className="grid grid-cols-12 h-full">
        {digits.map((digit, index) => (
          <div key={index} className={`h-8 flex items-center justify-center ${
            index < 11 ? `border-r ${
              // 整数部分每三位加深分隔线，不包括角分
              index === 3 || index === 6 
                ? 'border-gray-400 border-r-2' 
                : index === 9 
                  ? 'border-gray-400 border-r-2'  // 元位加深
                  : 'border-gray-300'
            }` : ''
          }`}>
            {digit === ' ' ? '' : digit}
          </div>
        ))}
      </div>
    </td>
  );
};

const VoucherEntry = ({ index, summary, account, amount, isDebit, balance }) => {
  return (
    <tr className="border-b border-gray-200 h-9">
      <td className="py-1 px-2 border-r border-gray-200 text-center w-12">{index}</td>
      <td className="py-1 px-2 border-r border-gray-200">{summary}</td>
      <td className="py-1 px-2 border-r border-gray-200">
        <div className="flex items-center">
          <span className="flex-1">{account}</span>
          {balance && (
            <span className="text-gray-500 text-sm">
              余额: {balance.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}
            </span>
          )}
        </div>
      </td>
      <AmountCell 
        amount={isDebit ? amount : null}
        className="border-r border-gray-200 w-[240px]"
      />
      <AmountCell 
        amount={!isDebit ? amount : null}
        className="w-[240px]"
      />
    </tr>
  );
};

// 1. 修改 props，接收 editable，默认 false
const Voucher = ({ voucher = {}, index = 0, editable = false }) => {
  // 2. isEditing 只在 editable 为 true 时才允许切换，初始值为 editable
  const [isExpanded, setIsExpanded] = useState(true);
  const [isEditing, setIsEditing] = useState(editable);

  // 只要 editable 变为 false，isEditing 立即变为 false
  useEffect(() => {
    if (!editable) setIsEditing(false);
  }, [editable]);

  const entries = voucher.entries || [];
  const date = voucher.date || new Date().toISOString().split('T')[0];
  const summary = voucher.summary || '';

  const attachments = voucher.attachments || 0;
  const creator = voucher.creator || '系统';
  const reviewer = voucher.reviewer;

  // 计算借方和贷方的总金额
  const debitTotal = entries
    .filter(entry => entry.type === 'debit')
    .reduce((sum, entry) => sum + (entry.amount || 0), 0);

  const creditTotal = entries
    .filter(entry => entry.type === 'credit')
    .reduce((sum, entry) => sum + (entry.amount || 0), 0);

  const amountUnits = ['亿', '千', '百', '十', '万', '千', '百', '十', '元', '角', '分'];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* 凭证头部 */}
      <div className="p-4 flex items-center justify-between border-b border-gray-200">
        <div className="flex items-center space-x-6">
          <h3 className="text-xl font-medium">记账凭证</h3>
          <div className="flex items-center space-x-4 text-gray-600">
            <span>{new Date().getFullYear()}年第{index + 1}期</span>
            <div className="flex items-center">
              <Calendar size={16} className="mr-1" />
              <span>{date}</span>
            </div>
            {attachments > 0 && (
              <div className="flex items-center text-blue-500">
                <Paperclip size={16} className="mr-1" />
                <span>附件 {attachments} 张</span>
              </div>
            )}
          </div>
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-gray-400 hover:text-gray-600 p-1 hover:bg-gray-100 rounded-full transition-colors"
        >
          {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
        </button>
      </div>

      {/* 凭证主体 */}
      <div className={`overflow-hidden transition-all duration-200 ${isExpanded ? 'max-h-[800px]' : 'max-h-0'}`}>
        <div className="w-full">
          {/* 编辑按钮只在 editable 为 true 且非编辑状态时显示 */}
          {editable && !isEditing && (
            <div className="flex justify-end mb-2">
              <button className="text-blue-500 text-sm" onClick={() => setIsEditing(true)}>编辑</button>
            </div>
          )}
          {/* 编辑区只在 isEditing && editable 时显示 */}
          {isEditing && editable ? (
            <div>编辑区内容（略）</div>
          ) : (
            <table className="w-full border-collapse">
              <thead>
                <tr className="bg-gray-50">
                  <th className="py-2 px-2 border-b border-r border-gray-200 text-center w-12">序号</th>
                  <th className="py-2 px-2 border-b border-r border-gray-200 text-left">摘要</th>
                  <th className="py-2 px-2 border-b border-r border-gray-200 text-left">科目</th>
                  <th className="py-2 px-2 border-b border-r border-gray-200 text-center w-[240px]">
                    <div>借方金额</div>
                    <div className="relative">
                      <div className="grid grid-cols-12 text-xs font-normal">
                        {amountUnits.map((unit, index) => (
                          <div key={index} className="h-4 relative">
                            <div className={`absolute right-0 top-1/2 -translate-y-1/2 transform ${
                              index < 11 ? `border-r ${
                                // 整数部分每三位加深分隔线，不包括角分
                                index === 3 || index === 6 
                                  ? 'border-gray-400 border-r-2' 
                                  : index === 9 
                                    ? 'border-gray-400 border-r-2'  // 元位加深
                                    : 'border-gray-300'
                              } h-8` : ''
                            }`}></div>
                            <div className="absolute right-[-50%] top-0 transform whitespace-nowrap">
                              {unit}
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="h-4"></div>
                    </div>
                  </th>
                  <th className="py-2 px-2 border-b border-gray-200 text-center w-[240px]">
                    <div>贷方金额</div>
                    <div className="relative">
                      <div className="grid grid-cols-12 text-xs font-normal">
                        {amountUnits.map((unit, index) => (
                          <div key={index} className="h-4 relative">
                            <div className={`absolute right-0 top-1/2 -translate-y-1/2 transform ${
                              index < 11 ? `border-r ${
                                // 整数部分每三位加深分隔线，不包括角分
                                index === 3 || index === 6 
                                  ? 'border-gray-400 border-r-2' 
                                  : index === 9 
                                    ? 'border-gray-400 border-r-2'  // 元位加深
                                    : 'border-gray-300'
                              } h-8` : ''
                            }`}></div>
                            <div className="absolute right-[-50%] top-0 transform whitespace-nowrap">
                              {unit}
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="h-4"></div>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                {entries.map((entry, idx) => (
                  <VoucherEntry
                    key={idx}
                    index={idx + 1}
                    summary={summary}
                    account={entry.account || ''}
                    amount={entry.amount || 0}
                    isDebit={entry.type === 'debit'}
                    balance={entry.balance}
                  />
                ))}
                {/* 空行 */}
                {Array.from({ length: Math.max(0, 4 - entries.length) }).map((_, idx) => (
                  <tr key={`empty-${idx}`} className="border-b border-gray-200 h-9">
                    <td className="py-1 px-2 border-r border-gray-200 text-center w-12">{entries.length + idx + 1}</td>
                    <td className="py-1 px-2 border-r border-gray-200">&nbsp;</td>
                    <td className="py-1 px-2 border-r border-gray-200">&nbsp;</td>
                    <AmountCell className="border-r border-gray-200 w-[240px]" />
                    <AmountCell className="w-[240px]" />
                  </tr>
                ))}
                {/* 合计行 */}
                <tr className="bg-gray-50 font-medium h-9">
                  <td className="py-1 px-2 border-t border-r border-gray-200 text-center" colSpan="3">
                    合计
                  </td>
                  <AmountCell 
                    amount={debitTotal}
                    className="border-t border-r border-gray-200 w-[240px] font-bold"
                  />
                  <AmountCell 
                    amount={creditTotal}
                    className="border-t w-[240px] font-bold"
                  />
                </tr>
              </tbody>
            </table>
          )}
        </div>
        {/* 凭证底部 */}
        <div className="p-4 bg-gray-50 text-sm text-gray-600 border-t border-gray-200">
          <div className="flex justify-between">
            <span>制单人：{creator}</span>
            <span>审核人：{reviewer || '待审核'}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Voucher;
