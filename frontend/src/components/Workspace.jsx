import React from 'react';
import Voucher from './Voucher';
import CollapsibleCard from './CollapsibleCard';

const Workspace = ({ children, vouchers, subjects, assets, staffs }) => {
  return (
    <div className="flex-1 flex flex-col h-full overflow-hidden bg-gray-100">
      <div className="flex-1 overflow-auto p-6">
        <div>
          {/* 科目卡片 */}
          {subjects && subjects.length > 0 && (
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-800">生成的科目</h2>
              <p className="text-sm text-gray-500 mt-1">共 {subjects.length} 个科目</p>
              <div className="space-y-4 mt-4">
                {subjects.map((subject, index) => (
                  <CollapsibleCard
                    key={index}
                    title={`科目：${subject.科目编码 || ''} ${subject.科目名称 || ''}`.trim()}
                    date={subject.创建日期 || ''}
                  >
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div><span className="font-medium">科目编码:</span> {subject.科目编码}</div>
                      <div><span className="font-medium">科目名称:</span> {subject.科目名称}</div>
                      <div><span className="font-medium">类别:</span> {subject.类别}</div>
                      <div><span className="font-medium">方向:</span> {subject.方向}</div>
                      {subject.备注 && <div className="col-span-2"><span className="font-medium">备注:</span> {subject.备注}</div>}
                    </div>
                  </CollapsibleCard>
                ))}
              </div>
            </div>
          )}

          {/* 资产卡片 */}
          {assets && assets.length > 0 && (
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-800">生成的资产</h2>
              <p className="text-sm text-gray-500 mt-1">共 {assets.length} 个资产</p>
              <div className="space-y-4 mt-4">
                {assets.map((asset, index) => (
                  <CollapsibleCard
                    key={index}
                    title={`资产：${asset.资产编码 || ''} ${asset.资产名称 || ''}`.trim()}
                    date={asset.购置日期 || ''}
                  >
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div><span className="font-medium">资产编码:</span> {asset.资产编码}</div>
                      <div><span className="font-medium">资产名称:</span> {asset.资产名称}</div>
                      <div><span className="font-medium">类别:</span> {asset.类别}</div>
                      <div><span className="font-medium">原值:</span> {asset.原值}</div>
                      <div><span className="font-medium">净值:</span> {asset.净值}</div>
                      <div><span className="font-medium">购置日期:</span> {asset.购置日期}</div>
                      <div><span className="font-medium">使用年限:</span> {asset.使用年限}</div>
                      <div><span className="font-medium">状态:</span> {asset.状态}</div>
                      {asset.备注 && <div className="col-span-2"><span className="font-medium">备注:</span> {asset.备注}</div>}
                    </div>
                  </CollapsibleCard>
                ))}
              </div>
            </div>
          )}

          {/* 员工卡片 */}
          {staffs && staffs.length > 0 && (
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-800">生成的员工</h2>
              <p className="text-sm text-gray-500 mt-1">共 {staffs.length} 个员工</p>
              <div className="space-y-4 mt-4">
                {staffs.map((staff, index) => (
                  <CollapsibleCard
                    key={index}
                    title={`员工：${staff.工号 || ''} ${staff.姓名 || ''}`.trim()}
                    date={staff.入职日期 || ''}
                  >
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div><span className="font-medium">工号:</span> {staff.工号}</div>
                      <div><span className="font-medium">姓名:</span> {staff.姓名}</div>
                      <div><span className="font-medium">岗位编码:</span> {staff.岗位编码}</div>
                      <div><span className="font-medium">电话:</span> {staff.电话}</div>
                      <div><span className="font-medium">状态:</span> {staff.状态}</div>
                      {staff.备注 && <div className="col-span-2"><span className="font-medium">备注:</span> {staff.备注}</div>}
                    </div>
                  </CollapsibleCard>
                ))}
              </div>
            </div>
          )}

          {/* 凭证卡片 */}
          {vouchers && vouchers.length > 0 && (
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-800">生成的凭证</h2>
              <p className="text-sm text-gray-500 mt-1">共 {vouchers.length} 张凭证</p>
              <div className="space-y-4 mt-4">
                {vouchers.map((voucher, index) => (
                  <Voucher 
                    key={index} 
                    voucher={{
                      ...voucher,
                      date: voucher.日期 || voucher.date,
                      summary: voucher.摘要 || voucher.summary,
                      entries: [
                        ...((Array.isArray(voucher.借方) ? voucher.借方 : voucher.debit || [])).map(item => ({
                          ...item,
                          account: `${item.科目编码 || ''} ${item.科目名称 || ''}`.trim(),
                          amount: parseFloat(item.金额) || parseFloat(item.amount) || 0,
                          type: 'debit'
                        })),
                        ...((Array.isArray(voucher.贷方) ? voucher.贷方 : voucher.credit || [])).map(item => ({
                          ...item,
                          account: `${item.科目编码 || ''} ${item.科目名称 || ''}`.trim(),
                          amount: parseFloat(item.金额) || parseFloat(item.amount) || 0,
                          type: 'credit'
                        }))
                      ]
                    }}
                    index={index}
                    editable={false}
                  />
                ))}
              </div>
            </div>
          )}

          {/* 如果没有任何卡片 */}
          {(!subjects || subjects.length === 0) && 
           (!assets || assets.length === 0) && 
           (!staffs || staffs.length === 0) && 
           (!vouchers || vouchers.length === 0) && (
            <div className="text-center py-12 bg-white rounded-lg shadow-sm">
              <p className="text-gray-500">暂无生成的卡片</p>
            </div>
          )}

          {/* children 作为附加内容渲染 */}
          {children}
        </div>
      </div>
    </div>
  );
};

export default Workspace;
