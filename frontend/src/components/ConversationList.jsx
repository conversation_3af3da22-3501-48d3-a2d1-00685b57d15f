import React from 'react'
import { Plus, Loader } from 'lucide-react'

const ConversationList = ({ sessions, activeId, onSelect, onNew }) => {
  return (
    <div className="border-b border-gray-200 p-2 space-y-1">
      <button
        onClick={onNew}
        className="w-full flex items-center justify-center px-3 py-2 mb-1 text-sm text-gray-600 hover:bg-gray-100 rounded"
      >
        <Plus size={16} className="mr-1" /> 新建对话
      </button>
      <div className="overflow-y-auto max-h-60 space-y-1 pr-1">
        {sessions.map((s, idx) => (
          <button
            key={s.id}
            onClick={() => onSelect(s.id)}
            className={`w-full flex items-center justify-between px-3 py-2 rounded text-sm transition-colors ${
              activeId === s.id ? 'bg-blue-50 text-blue-600' : 'hover:bg-gray-100 text-gray-700'
            }`}
          >
            <span className="truncate mr-2">{s.name || `会话${idx + 1}`}</span>
            {s.isLoading && <Loader className="animate-spin" size={14} />}
          </button>
        ))}
      </div>
    </div>
  )
}

export default ConversationList 