import React, { useEffect, useState } from 'react';

const REPORT_TYPES = [
  { key: 'balance-sheet', label: '资产负债表' },
  { key: 'income-statement', label: '利润表' },
  { key: 'cash-flow', label: '现金流量表' },
];

const API_MAP = {
  'balance-sheet': {
    fetch: 'http://localhost:8000/reports/balance-sheet',
    export: 'http://localhost:8000/reports/balance-sheet/export',
  },
  'income-statement': {
    fetch: 'http://localhost:8000/reports/income-statement',
    export: 'http://localhost:8000/reports/income-statement/export',
  },
  'cash-flow': {
    fetch: 'http://localhost:8000/reports/cash-flow',
    export: 'http://localhost:8000/reports/cash-flow/export',
  },
};

const Report = () => {
  const [reportType, setReportType] = useState('balance-sheet');
  const [report, setReport] = useState([]);

  const fetchReport = async () => {
    const url = API_MAP[reportType].fetch;
    const res = await fetch(url);
    const data = await res.json();
    setReport(data.report || []);
  };

  useEffect(() => {
    fetchReport();
    // eslint-disable-next-line
  }, [reportType]);

  const handleExport = () => {
    const url = API_MAP[reportType].export;
    window.open(url, '_blank');
  };

  return (
    <div className="flex-1 flex flex-col h-full overflow-hidden bg-gray-100">
      <div className="flex-1 overflow-auto p-6">
        <div className="flex items-center mb-4 space-x-4">
          {REPORT_TYPES.map(t => (
            <button
              key={t.key}
              className={`px-4 py-2 rounded ${reportType === t.key ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'}`}
              onClick={() => setReportType(t.key)}
            >
              {t.label}
            </button>
          ))}
          <button className="ml-auto px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600" onClick={handleExport}>导出</button>
        </div>
        <table className="w-full bg-white rounded shadow">
          <thead>
            <tr>
              <th className="px-4 py-2 border">项目</th>
              <th className="px-4 py-2 border">本期金额</th>
              <th className="px-4 py-2 border">上期金额</th>
              <th className="px-4 py-2 border">去年同期金额</th>
              <th className="px-4 py-2 border">差额</th>
            </tr>
          </thead>
          <tbody>
            {report.length === 0 ? (
              <tr><td colSpan={5} className="text-center py-8 text-gray-400">暂无数据</td></tr>
            ) : report.map((row, idx) => {
              const current = row.current ?? row.amount ?? '';
              const last = row.last ?? '';
              const lastYear = row.lastYear ?? row.last_year ?? '';
              // 差额 = 本期 - 上期（如有），否则空
              const diff = (current !== '' && last !== '') ? (current - last) : '';
              return (
                <tr key={idx}>
                  <td className="px-4 py-2 border">{row.item}</td>
                  <td className="px-4 py-2 border text-right">{current !== '' ? Number(current).toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : ''}</td>
                  <td className="px-4 py-2 border text-right">{last !== '' ? Number(last).toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : ''}</td>
                  <td className="px-4 py-2 border text-right">{lastYear !== '' ? Number(lastYear).toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : ''}</td>
                  <td className={"px-4 py-2 border text-right " + (diff > 0 ? 'text-green-600' : diff < 0 ? 'text-red-600' : '')}>
                    {diff !== '' ? Number(diff).toLocaleString('zh-CN', { minimumFractionDigits: 2 }) : ''}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Report;
