import React, { useEffect, useState } from 'react';

const LEDGER_TYPES = [
  { key: 'general', label: '总账' },
  { key: 'detail', label: '明细账' },
  { key: 'journal', label: '日记账' },
];

const Bookkeeping = () => {
  const [ledgerType, setLedgerType] = useState('general');
  const [ledger, setLedger] = useState([]);
  const [accountCode, setAccountCode] = useState('');
  const [subjects, setSubjects] = useState([]);

  // 加载科目表
  useEffect(() => {
    fetch('http://localhost:8000/subjects')
      .then(res => res.json())
      .then(data => setSubjects(data.subjects || []));
  }, []);

  // 加载账簿数据
  const fetchLedger = async () => {
    let url = 'http://localhost:8000/ledger?';
    if (ledgerType === 'detail' && accountCode) {
      url += `account_code=${accountCode}&detail=true`;
    } else if (ledgerType === 'general' && accountCode) {
      url += `account_code=${accountCode}`;
    }
    const res = await fetch(url);
    const data = await res.json();
    setLedger(data.ledger || []);
  };

  useEffect(() => {
    fetchLedger();
    // eslint-disable-next-line
  }, [ledgerType, accountCode]);

  // 导出账簿
  const handleExport = () => {
    let url = 'http://localhost:8000/ledger/export?';
    if (ledgerType === 'detail' && accountCode) {
      url += `account_code=${accountCode}&detail=true`;
    } else if (ledgerType === 'general' && accountCode) {
      url += `account_code=${accountCode}`;
    }
    window.open(url, '_blank');
  };

  return (
    <div className="p-6">
      <div className="flex items-center mb-4 space-x-4">
        {LEDGER_TYPES.map(t => (
          <button
            key={t.key}
            className={`px-4 py-2 rounded ${ledgerType === t.key ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-700'}`}
            onClick={() => setLedgerType(t.key)}
          >
            {t.label}
          </button>
        ))}
        {(ledgerType === 'general' || ledgerType === 'detail') && (
          <select
            className="ml-4 px-2 py-1 border rounded"
            value={accountCode}
            onChange={e => setAccountCode(e.target.value)}
          >
            <option value="">全部科目</option>
            {subjects.map(s => (
              <option key={s.code} value={s.code}>{s.code} {s.name}</option>
            ))}
          </select>
        )}
        <button className="ml-auto px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600" onClick={handleExport}>导出</button>
      </div>
      <div className="overflow-x-auto">
        <table className="min-w-full bg-white rounded shadow">
          <thead>
            <tr>
              <th className="px-4 py-2 border">日期</th>
              <th className="px-4 py-2 border">凭证号</th>
              <th className="px-4 py-2 border">摘要</th>
              <th className="px-4 py-2 border">科目编码</th>
              <th className="px-4 py-2 border">科目名称</th>
              <th className="px-4 py-2 border">借方</th>
              <th className="px-4 py-2 border">贷方</th>
              <th className="px-4 py-2 border">余额</th>
              <th className="px-4 py-2 border">方向</th>
            </tr>
          </thead>
          <tbody>
            {ledger.length === 0 ? (
              <tr><td colSpan={9} className="text-center py-8 text-gray-400">暂无数据</td></tr>
            ) : ledger.map((e, idx) => (
              <tr key={idx}>
                <td className="px-4 py-2 border">{e.date}</td>
                <td className="px-4 py-2 border">{e.voucher_no}</td>
                <td className="px-4 py-2 border">{e.summary}</td>
                <td className="px-4 py-2 border">{e.account_code}</td>
                <td className="px-4 py-2 border">{e.account_name}</td>
                <td className="px-4 py-2 border text-right">{e.debit?.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}</td>
                <td className="px-4 py-2 border text-right">{e.credit?.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}</td>
                <td className="px-4 py-2 border text-right">{e.balance?.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}</td>
                <td className="px-4 py-2 border">{e.direction}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Bookkeeping;
