import React, { useEffect, useState, useRef } from 'react';

const RoleStaffConfig = () => {
  // 岗位
  const [roles, setRoles] = useState([]);
  const [editRole, setEditRole] = useState(null);
  const [roleForm, setRoleForm] = useState({ code: '', name: '', description: '' });
  const roleFileInputRef = useRef();
  // 人员
  const [staffs, setStaffs] = useState([]);
  const [editStaff, setEditStaff] = useState(null);
  const [staffForm, setStaffForm] = useState({ job_no: '', name: '', role_code: '', phone: '', status: '', remark: '' });
  const staffFileInputRef = useRef();

  // 加载岗位
  const fetchRoles = async () => {
    const res = await fetch('http://localhost:8000/roles');
    const data = await res.json();
    setRoles(data.roles || []);
  };
  // 加载人员
  const fetchStaffs = async () => {
    const res = await fetch('http://localhost:8000/staffs');
    const data = await res.json();
    setStaffs(data.staffs || []);
  };
  useEffect(() => {
    fetchRoles();
    fetchStaffs();
  }, []);

  // 岗位导出
  const handleRoleExport = () => {
    window.open('http://localhost:8000/roles/export', '_blank');
  };
  // 岗位导入
  const handleRoleImport = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    const formData = new FormData();
    formData.append('file', file);
    await fetch('http://localhost:8000/roles/import', { method: 'POST', body: formData });
    fetchRoles();
    roleFileInputRef.current.value = '';
  };
  // 岗位删除
  const handleRoleDelete = async (code) => {
    if (!window.confirm('确定要删除该岗位吗？')) return;
    await fetch(`http://localhost:8000/roles/${code}`, { method: 'DELETE' });
    fetchRoles();
  };
  // 岗位编辑
  const openEditRole = (role) => { setEditRole(role); setRoleForm(role); };
  const closeEditRole = () => { setEditRole(null); };
  const handleRoleEditSubmit = async (e) => {
    e.preventDefault();
    await fetch(`http://localhost:8000/roles/${roleForm.code}`, {
      method: 'PUT', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(roleForm),
    });
    closeEditRole(); fetchRoles();
  };

  // 人员导出
  const handleStaffExport = () => {
    window.open('http://localhost:8000/staffs/export', '_blank');
  };
  // 人员导入
  const handleStaffImport = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    const formData = new FormData();
    formData.append('file', file);
    await fetch('http://localhost:8000/staffs/import', { method: 'POST', body: formData });
    fetchStaffs();
    staffFileInputRef.current.value = '';
  };
  // 人员删除
  const handleStaffDelete = async (job_no) => {
    if (!window.confirm('确定要删除该人员吗？')) return;
    await fetch(`http://localhost:8000/staffs/${job_no}`, { method: 'DELETE' });
    fetchStaffs();
  };
  // 人员编辑
  const openEditStaff = (staff) => { setEditStaff(staff); setStaffForm(staff); };
  const closeEditStaff = () => { setEditStaff(null); };
  const handleStaffEditSubmit = async (e) => {
    e.preventDefault();
    await fetch(`http://localhost:8000/staffs/${staffForm.job_no}`, {
      method: 'PUT', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(staffForm),
    });
    closeEditStaff(); fetchStaffs();
  };

  return (
    <div className="p-6 space-y-10">
      {/* 岗位管理 */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-xl font-semibold">岗位管理</h2>
          <div className="space-x-2">
            <input type="file" accept=".csv" ref={roleFileInputRef} style={{ display: 'none' }} onChange={handleRoleImport} />
            <button className="px-3 py-1 bg-blue-500 text-white rounded" onClick={() => roleFileInputRef.current.click()}>导入</button>
            <button className="px-3 py-1 bg-green-500 text-white rounded" onClick={handleRoleExport}>导出</button>
          </div>
        </div>
        <table className="min-w-full bg-white rounded shadow">
          <thead>
            <tr>
              <th className="px-4 py-2 border">岗位编号</th>
              <th className="px-4 py-2 border">岗位名称</th>
              <th className="px-4 py-2 border">描述</th>
              <th className="px-4 py-2 border">操作</th>
            </tr>
          </thead>
          <tbody>
            {roles.length === 0 ? (
              <tr><td colSpan={4} className="text-center py-8 text-gray-400">暂无数据</td></tr>
            ) : roles.map((r, idx) => (
              <tr key={r.code}>
                <td className="px-4 py-2 border">{r.code}</td>
                <td className="px-4 py-2 border">{r.name}</td>
                <td className="px-4 py-2 border">{r.description}</td>
                <td className="px-4 py-2 border">
                  <button className="text-blue-500 hover:underline" onClick={() => openEditRole(r)}>编辑</button>
                  <button className="text-red-500 hover:underline ml-2" onClick={() => handleRoleDelete(r.code)}>删除</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {/* 岗位编辑弹窗 */}
        {editRole && (
          <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
            <form className="bg-white p-6 rounded shadow w-96" onSubmit={handleRoleEditSubmit}>
              <h3 className="text-lg font-semibold mb-4">编辑岗位</h3>
              <div className="mb-3">
                <label className="block mb-1">岗位编号</label>
                <input className="w-full border rounded px-2 py-1" value={roleForm.code} disabled />
              </div>
              <div className="mb-3">
                <label className="block mb-1">岗位名称</label>
                <input className="w-full border rounded px-2 py-1" value={roleForm.name} onChange={e => setRoleForm(f => ({ ...f, name: e.target.value }))} required />
              </div>
              <div className="mb-3">
                <label className="block mb-1">描述</label>
                <input className="w-full border rounded px-2 py-1" value={roleForm.description} onChange={e => setRoleForm(f => ({ ...f, description: e.target.value }))} />
              </div>
              <div className="flex justify-end space-x-2 mt-4">
                <button type="button" className="px-4 py-2 bg-gray-300 rounded" onClick={closeEditRole}>取消</button>
                <button type="submit" className="px-4 py-2 bg-blue-600 text-white rounded">保存</button>
              </div>
            </form>
          </div>
        )}
      </div>
      {/* 人员管理 */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-xl font-semibold">人员管理</h2>
          <div className="space-x-2">
            <input type="file" accept=".csv" ref={staffFileInputRef} style={{ display: 'none' }} onChange={handleStaffImport} />
            <button className="px-3 py-1 bg-blue-500 text-white rounded" onClick={() => staffFileInputRef.current.click()}>导入</button>
            <button className="px-3 py-1 bg-green-500 text-white rounded" onClick={handleStaffExport}>导出</button>
          </div>
        </div>
        <table className="min-w-full bg-white rounded shadow">
          <thead>
            <tr>
              <th className="px-4 py-2 border">工号</th>
              <th className="px-4 py-2 border">姓名</th>
              <th className="px-4 py-2 border">岗位编号</th>
              <th className="px-4 py-2 border">联系方式</th>
              <th className="px-4 py-2 border">状态</th>
              <th className="px-4 py-2 border">备注</th>
              <th className="px-4 py-2 border">操作</th>
            </tr>
          </thead>
          <tbody>
            {staffs.length === 0 ? (
              <tr><td colSpan={7} className="text-center py-8 text-gray-400">暂无数据</td></tr>
            ) : staffs.map((s, idx) => (
              <tr key={s.job_no}>
                <td className="px-4 py-2 border">{s.job_no}</td>
                <td className="px-4 py-2 border">{s.name}</td>
                <td className="px-4 py-2 border">{s.role_code}</td>
                <td className="px-4 py-2 border">{s.phone}</td>
                <td className="px-4 py-2 border">{s.status}</td>
                <td className="px-4 py-2 border">{s.remark}</td>
                <td className="px-4 py-2 border">
                  <button className="text-blue-500 hover:underline" onClick={() => openEditStaff(s)}>编辑</button>
                  <button className="text-red-500 hover:underline ml-2" onClick={() => handleStaffDelete(s.job_no)}>删除</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {/* 人员编辑弹窗 */}
        {editStaff && (
          <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
            <form className="bg-white p-6 rounded shadow w-96" onSubmit={handleStaffEditSubmit}>
              <h3 className="text-lg font-semibold mb-4">编辑人员</h3>
              <div className="mb-3">
                <label className="block mb-1">工号</label>
                <input className="w-full border rounded px-2 py-1" value={staffForm.job_no} disabled />
              </div>
              <div className="mb-3">
                <label className="block mb-1">姓名</label>
                <input className="w-full border rounded px-2 py-1" value={staffForm.name} onChange={e => setStaffForm(f => ({ ...f, name: e.target.value }))} required />
              </div>
              <div className="mb-3">
                <label className="block mb-1">岗位编号</label>
                <input className="w-full border rounded px-2 py-1" value={staffForm.role_code} onChange={e => setStaffForm(f => ({ ...f, role_code: e.target.value }))} required />
              </div>
              <div className="mb-3">
                <label className="block mb-1">联系方式</label>
                <input className="w-full border rounded px-2 py-1" value={staffForm.phone} onChange={e => setStaffForm(f => ({ ...f, phone: e.target.value }))} />
              </div>
              <div className="mb-3">
                <label className="block mb-1">状态</label>
                <input className="w-full border rounded px-2 py-1" value={staffForm.status} onChange={e => setStaffForm(f => ({ ...f, status: e.target.value }))} />
              </div>
              <div className="mb-3">
                <label className="block mb-1">备注</label>
                <input className="w-full border rounded px-2 py-1" value={staffForm.remark} onChange={e => setStaffForm(f => ({ ...f, remark: e.target.value }))} />
              </div>
              <div className="flex justify-end space-x-2 mt-4">
                <button type="button" className="px-4 py-2 bg-gray-300 rounded" onClick={closeEditStaff}>取消</button>
                <button type="submit" className="px-4 py-2 bg-blue-600 text-white rounded">保存</button>
              </div>
            </form>
          </div>
        )}
      </div>
    </div>
  );
};

export default RoleStaffConfig; 