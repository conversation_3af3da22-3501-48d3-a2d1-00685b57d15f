import React from 'react';

const Resizer = ({ onResize }) => {
  const handleMouseDown = (e) => {
    e.preventDefault();
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = (e) => {
    if (onResize) {
      onResize(e.clientX);
    }
  };

  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  return (
    <div
      className="w-1 hover:w-2 bg-transparent hover:bg-gray-300 cursor-col-resize transition-all duration-200 absolute right-0 top-0 bottom-0 z-10"
      onMouseDown={handleMouseDown}
    >
      <div className="absolute inset-0 hover:opacity-100 opacity-0 transition-opacity">
        <div className="w-px h-full mx-auto bg-gray-300"></div>
      </div>
    </div>
  );
};

export default Resizer; 