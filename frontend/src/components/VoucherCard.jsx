import React, { useState } from 'react';
import { Plus, Minus } from 'lucide-react';

const VoucherCard = (props) => {
  const { onConfirm = () => {}, editable = true, ...rest } = props;
  console.log('VoucherCard props', props);
  if (!props) return null;
  
  const [form, setForm] = useState(rest || {
    日期: '',
    摘要: '',
    借方: [],
    贷方: []
  });
  const [isEditing, setIsEditing] = useState(editable);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const addDebitEntry = () => {
    setForm({
      ...form,
      借方: [...form.借方, { 科目名称: '', 科目编码: '', 金额: '' }]
    });
  };

  const addCreditEntry = () => {
    setForm({
      ...form,
      贷方: [...form.贷方, { 科目名称: '', 科目编码: '', 金额: '' }]
    });
  };

  const updateDebitEntry = (index, field, value) => {
    const newDebits = [...form.借方];
    newDebits[index] = { ...newDebits[index], [field]: value };
    setForm({ ...form, 借方: newDebits });
  };

  const updateCreditEntry = (index, field, value) => {
    const newCredits = [...form.贷方];
    newCredits[index] = { ...newCredits[index], [field]: value };
    setForm({ ...form, 贷方: newCredits });
  };

  const removeDebitEntry = (index) => {
    const newDebits = form.借方.filter((_, i) => i !== index);
    setForm({ ...form, 借方: newDebits });
  };

  const removeCreditEntry = (index) => {
    const newCredits = form.贷方.filter((_, i) => i !== index);
    setForm({ ...form, 贷方: newCredits });
  };

  const getTotalDebit = () => {
    return form.借方.reduce((sum, entry) => sum + (parseFloat(entry.金额) || 0), 0);
  };

  const getTotalCredit = () => {
    return form.贷方.reduce((sum, entry) => sum + (parseFloat(entry.金额) || 0), 0);
  };

  const isBalanced = () => {
    return Math.abs(getTotalDebit() - getTotalCredit()) < 0.01;
  };

  const handleConfirm = () => {
    if (!isBalanced()) {
      alert('借贷方金额不平衡，请检查！');
      return;
    }
    if (onConfirm) onConfirm(form);
    setIsEditing(false);
  };

  const date = form.日期 || '';
  const summary = form.摘要 || '';
  const debits = Array.isArray(form.借方) ? form.借方 : [];
  const credits = Array.isArray(form.贷方) ? form.贷方 : [];
  const entries = [
    ...debits.map(entry => ({ ...entry, type: 'debit' })),
    ...credits.map(entry => ({ ...entry, type: 'credit' }))
  ];

  return (
    <div className="bg-white rounded-lg shadow p-4 border border-gray-200 mb-2 max-w-full">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-lg font-semibold">凭证卡片</h3>
        {isEditing ? null : (
          <button className="text-blue-500 text-sm" onClick={() => setIsEditing(true)}>编辑</button>
        )}
      </div>
      
      {isEditing ? (
        <div className="space-y-4">
          <div>
            <label className="block text-sm text-gray-600">日期</label>
            <input 
              name="日期" 
              value={form.日期} 
              onChange={handleChange} 
              className="w-full border rounded p-1" 
              placeholder="YYYY-MM-DD"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-600">摘要</label>
            <input 
              name="摘要" 
              value={form.摘要} 
              onChange={handleChange} 
              className="w-full border rounded p-1" 
            />
          </div>

          
          <div>
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm text-gray-600 font-semibold">借方</label>
              <button onClick={addDebitEntry} className="text-blue-500 hover:text-blue-700 p-1 rounded hover:bg-blue-50">
                <Plus size={16} />
              </button>
            </div>
            {form.借方.map((entry, index) => (
              <div key={index} className="flex space-x-2 mb-2">
                <input 
                  placeholder="编码 科目名称" 
                  value={`${entry.科目编码 || ''} ${entry.科目名称 || ''}`}
                  onChange={(e) => {
                    const value = e.target.value;
                    // 解析科目编码和名称
                    const codeMatch = value.match(/^(\S+)\s+(.+)$/);
                    const code = codeMatch ? codeMatch[1].trim() : '';
                    const name = codeMatch ? codeMatch[2].trim() : value;
                    updateDebitEntry(index, '科目编码', code);
                    updateDebitEntry(index, '科目名称', name);
                  }}
                  className="flex-1 border rounded p-1"
                />
                <input 
                  placeholder="金额" 
                  type="number"
                  value={entry.金额} 
                  onChange={(e) => updateDebitEntry(index, '金额', e.target.value)}
                  className="w-1/4 border rounded p-1"
                />
                <button onClick={() => removeDebitEntry(index)} className="text-red-500 hover:text-red-700 p-1 rounded hover:bg-red-50">
                  <Minus size={16} />
                </button>
              </div>
            ))}
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-2">
              <label className="block text-sm text-gray-600 font-semibold">贷方</label>
              <button onClick={addCreditEntry} className="text-blue-500 hover:text-blue-700 p-1 rounded hover:bg-blue-50">
                <Plus size={16} />
              </button>
            </div>
            {form.贷方.map((entry, index) => (
              <div key={index} className="flex space-x-2 mb-2">
                <input 
                  placeholder="编码 科目名称" 
                  value={`${entry.科目编码 || ''} ${entry.科目名称 || ''}`}
                  onChange={(e) => {
                    const value = e.target.value;
                    // 解析科目编码和名称
                    const codeMatch = value.match(/^(\S+)\s+(.+)$/);
                    const code = codeMatch ? codeMatch[1].trim() : '';
                    const name = codeMatch ? codeMatch[2].trim() : value;
                    updateCreditEntry(index, '科目编码', code);
                    updateCreditEntry(index, '科目名称', name);
                  }}
                  className="flex-1 border rounded p-1"
                />
                <input 
                  placeholder="金额" 
                  type="number"
                  value={entry.金额} 
                  onChange={(e) => updateCreditEntry(index, '金额', e.target.value)}
                  className="w-1/4 border rounded p-1"
                />
                <button onClick={() => removeCreditEntry(index)} className="text-red-500 hover:text-red-700 p-1 rounded hover:bg-red-50">
                  <Minus size={16} />
                </button>
              </div>
            ))}
          </div>
          
          <div className="flex justify-between items-center text-sm">
            <span>借方合计: {getTotalDebit().toFixed(2)}</span>
            <span>贷方合计: {getTotalCredit().toFixed(2)}</span>
            <span className={isBalanced() ? 'text-green-600' : 'text-red-600'}>
              {isBalanced() ? '✓ 平衡' : '✗ 不平衡'}
            </span>
          </div>
          
          <div className="flex justify-end space-x-2">
            <button onClick={() => setIsEditing(false)} className="px-4 py-1 border rounded">取消</button>
            <button onClick={handleConfirm} className="px-4 py-1 bg-blue-500 text-white rounded">确认</button>
          </div>
        </div>
      ) : (
        <div>
          <div className="text-sm text-gray-600 mb-1">日期：{date}</div>
          <div className="text-sm text-gray-600 mb-1">摘要：{summary}</div>

          <div className="mt-2">
            <table className="w-full text-sm border">
              <thead>
                <tr className="bg-gray-50">
                  <th className="border px-2 py-1 text-left w-2/3">科目</th>
                  <th className="border px-2 py-1 text-right w-1/6">借方金额</th>
                  <th className="border px-2 py-1 text-right w-1/6">贷方金额</th>
                </tr>
              </thead>
              <tbody>
                {entries.map((entry, idx) => (
                  <tr key={idx}>
                    <td className="border px-2 py-1 text-left">
                      {entry.科目编码 && `${entry.科目编码} `}{entry.科目名称}
                    </td>
                    <td className="border px-2 py-1 text-right">{entry.type === 'debit' ? entry.金额 : ''}</td>
                    <td className="border px-2 py-1 text-right">{entry.type === 'credit' ? entry.金额 : ''}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default VoucherCard; 