import React, { useState } from 'react';
import { ChevronDown, ChevronUp, Calendar } from 'lucide-react';

const CollapsibleCard = ({ title, date, children, defaultCollapsed = false }) => {
  const [collapsed, setCollapsed] = useState(defaultCollapsed);

  return (
    <div className="bg-white rounded-lg shadow p-4 border border-gray-200 mb-4">
      <div className="flex items-center justify-between border-b border-gray-200 pb-2 mb-2">
        <div className="flex items-center space-x-4">
          <span className="text-base font-medium">{title}</span>
          {date && (
            <div className="flex items-center text-gray-600 text-sm">
              <Calendar size={16} className="mr-1" />
              <span>{date}</span>
            </div>
          )}
        </div>
        <button
          onClick={() => setCollapsed(!collapsed)}
          className="text-gray-400 hover:text-gray-600 p-1 hover:bg-gray-100 rounded-full transition-colors"
        >
          {collapsed ? <ChevronDown size={20} /> : <ChevronUp size={20} />}
        </button>
      </div>
      {!collapsed && (
        <div className="mt-2">
          {children}
        </div>
      )}
    </div>
  );
};

export default CollapsibleCard; 