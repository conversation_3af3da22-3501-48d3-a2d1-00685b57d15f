import React, { useState } from 'react';

const StaffCard = (props) => {
  const { onConfirm = () => {}, editable = true, ...rest } = props;
  const [form, setForm] = useState(rest || {
    工号: '',
    姓名: '',
    岗位编码: '',
    电话: '',
    状态: '',
    备注: ''
  });
  const [isEditing, setIsEditing] = useState(editable);

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleConfirm = () => {
    if (onConfirm) onConfirm(form);
    setIsEditing(false);
  };

  return (
    <div className="bg-white rounded-lg shadow p-4 border border-gray-200 mb-2">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-lg font-semibold">员工卡片</h3>
        {isEditing ? null : (
          <button className="text-blue-500 text-sm" onClick={() => setIsEditing(true)}>编辑</button>
        )}
      </div>
      <div className="space-y-2">
        <div>
          <label className="block text-sm text-gray-600">工号</label>
          <input name="工号" value={form.工号} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">姓名</label>
          <input name="姓名" value={form.姓名} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">岗位编码</label>
          <input name="岗位编码" value={form.岗位编码} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">电话</label>
          <input name="电话" value={form.电话} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">状态</label>
          <input name="状态" value={form.状态} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
        <div>
          <label className="block text-sm text-gray-600">备注</label>
          <input name="备注" value={form.备注} onChange={handleChange} disabled={!isEditing} className="w-full border rounded p-1" />
        </div>
      </div>
      {isEditing && (
        <div className="mt-4 flex justify-end">
          <button className="bg-blue-500 text-white px-4 py-1 rounded" onClick={handleConfirm}>确认</button>
        </div>
      )}
    </div>
  );
};

export default StaffCard; 