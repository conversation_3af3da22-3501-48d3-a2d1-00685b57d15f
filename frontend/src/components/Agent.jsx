import { useState, useRef, useEffect, useCallback } from 'react';
import {
  Send, Upload, Image, File,
  RotateCcw, StopCircle, CheckCircle, Settings, Trash2,
  MessageSquare, Loader, AlertCircle
} from 'lucide-react';
import SubjectCard from './SubjectCard';
import AssetCard from './AssetCard';
import StaffCard from './StaffCard';
import VoucherCard from './VoucherCard';

// 消息类型枚举
const MESSAGE_TYPES = {
  USER: 'user',
  ASSISTANT: 'assistant',
  SYSTEM: 'system',
  FILE: 'file',
  CARD: 'card',
  ERROR: 'error'
};

// Toast组件
const Toast = ({ message, type = 'success', onClose }) => {
  useEffect(() => {
    const timer = setTimeout(onClose, 3000);
    return () => clearTimeout(timer);
  }, [onClose]);

  const bgColor = type === 'error' ? 'bg-red-500' : 'bg-green-500';
  const icon = type === 'error' ? <AlertCircle size={20} /> : <CheckCircle size={20} />;

  return (
    <div className={`fixed top-6 right-6 z-50 ${bgColor} text-white px-4 py-2 rounded shadow flex items-center animate-bounce-in`}>
      {icon}
      <span className="ml-2">{message}</span>
    </div>
  );
};

// 消息组件
const Message = ({ message, onRetry, onEdit }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content);

  const handleEdit = () => {
    if (isEditing) {
      onEdit(message.id, editContent);
      setIsEditing(false);
    } else {
      setIsEditing(true);
    }
  };

  const renderContent = () => {
    switch (message.type) {
      case MESSAGE_TYPES.USER:
        return (
          <div className="bg-blue-100 p-3 rounded-lg max-w-[80%] ml-auto">
            {isEditing ? (
              <div className="space-y-2">
                <textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  className="w-full p-2 border rounded resize-none"
                  rows={3}
                />
                <div className="flex gap-2">
                  <button
                    onClick={handleEdit}
                    className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
                  >
                    保存
                  </button>
                  <button
                    onClick={() => setIsEditing(false)}
                    className="px-3 py-1 bg-gray-500 text-white rounded text-sm"
                  >
                    取消
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex items-start justify-between">
                <span className="flex-1">{message.content}</span>
                <button
                  onClick={handleEdit}
                  className="ml-2 text-blue-600 hover:text-blue-800"
                  title="编辑消息"
                >
                  <Settings size={16} />
                </button>
              </div>
            )}
          </div>
        );

      case MESSAGE_TYPES.ASSISTANT:
        return (
          <div className="bg-gray-100 p-3 rounded-lg max-w-[80%]">
            <div className="flex items-start justify-between">
              <div className="flex-1 whitespace-pre-wrap">{message.content}</div>
              {message.error && (
                <button
                  onClick={() => onRetry(message.id)}
                  className="ml-2 text-red-600 hover:text-red-800"
                  title="重试"
                >
                  <RotateCcw size={16} />
                </button>
              )}
            </div>
          </div>
        );

      case MESSAGE_TYPES.FILE:
        return (
          <div className="bg-green-100 p-3 rounded-lg max-w-[80%] ml-auto">
            <div className="flex items-center gap-2">
              {message.fileType === 'image' ? <Image size={20} /> : <File size={20} />}
              <span>已上传: {message.fileName}</span>
              {message.processing && <Loader className="animate-spin" size={16} />}
            </div>
          </div>
        );

      case MESSAGE_TYPES.CARD:
        return (
          <div className="bg-yellow-50 p-3 rounded-lg max-w-full">
            {message.cards && message.cards.map((card, index) => (
              <div key={index} className="mb-2">
                {card.type === 'subject' && <SubjectCard {...card.props} />}
                {card.type === 'asset' && <AssetCard {...card.props} />}
                {card.type === 'staff' && <StaffCard {...card.props} />}
                {card.type === 'voucher' && <VoucherCard {...card.props} />}
              </div>
            ))}
          </div>
        );

      case MESSAGE_TYPES.ERROR:
        return (
          <div className="bg-red-100 p-3 rounded-lg max-w-[80%] border-l-4 border-red-500">
            <div className="flex items-start gap-2">
              <AlertCircle className="text-red-500 mt-0.5" size={16} />
              <div>
                <div className="font-medium text-red-800">错误</div>
                <div className="text-red-700">{message.content}</div>
              </div>
            </div>
          </div>
        );

      default:
        return <div className="p-3">{message.content}</div>;
    }
  };

  return (
    <div className="mb-4">
      <div className="flex items-start gap-2">
        <div className="flex-1">
          {renderContent()}
        </div>
      </div>
      <div className="text-xs text-gray-500 mt-1 px-3">
        {new Date(message.timestamp).toLocaleTimeString()}
      </div>
    </div>
  );
};

// 文件上传组件
const FileUpload = ({ onFileSelect, multiple = false, accept = "image/*,.pdf" }) => {
  const fileInputRef = useRef(null);
  const [dragOver, setDragOver] = useState(false);

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    const files = Array.from(e.dataTransfer.files);
    onFileSelect(multiple ? files : files[0]);
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    onFileSelect(multiple ? files : files[0]);
  };

  return (
    <div
      className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors ${
        dragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
      }`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      onClick={() => fileInputRef.current?.click()}
    >
      <input
        ref={fileInputRef}
        type="file"
        multiple={multiple}
        accept={accept}
        onChange={handleFileChange}
        className="hidden"
      />
      <Upload className="mx-auto mb-2 text-gray-400" size={24} />
      <p className="text-gray-600">
        {multiple ? '拖拽文件到这里或点击选择多个文件' : '拖拽文件到这里或点击选择文件'}
      </p>
      <p className="text-sm text-gray-500 mt-1">
        支持图片 (PNG, JPG, BMP, TIFF, WEBP) 和 PDF 文件
      </p>
    </div>
  );
};

// 主组件
const Agent = ({
  setVouchers, setSubjects, setAssets, setStaffs,
  aiConfig, inSidebar = false, session, updateSession
}) => {
  const [messages, setMessages] = useState(session?.messages || []);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState(session?.id || null);
  const [toast, setToast] = useState(null);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [abortController, setAbortController] = useState(null);
  
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);
  const streamingMessageRef = useRef(null);

  // 滚动到底部
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // 显示Toast
  const showToast = (message, type = 'success') => {
    setToast({ message, type });
  };

  // 处理卡片数据
  const processCards = (cards) => {
    cards.forEach(card => {
      if (card.type === 'subject' && card.data) {
        setSubjects(prev => [...prev, card.data]);
      } else if (card.type === 'asset' && card.data) {
        setAssets(prev => [...prev, card.data]);
      } else if (card.type === 'staff' && card.data) {
        setStaffs(prev => [...prev, card.data]);
      } else if (card.type === 'voucher' && card.data) {
        setVouchers(prev => [...prev, card.data]);
      }
    });
  };

  // 添加消息
  const addMessage = (content, type = MESSAGE_TYPES.ASSISTANT, extra = {}) => {
    const message = {
      id: Date.now() + Math.random(),
      content,
      type,
      timestamp: new Date(),
      ...extra
    };
    setMessages(prev => [...prev, message]);
    return message;
  };

  // 更新消息
  const updateMessage = (id, updates) => {
    setMessages(prev => prev.map(msg => 
      msg.id === id ? { ...msg, ...updates } : msg
    ));
  };

  // 配置智能体
  const configureAgent = async () => {
    try {
      const response = await fetch('http://localhost:8000/agent/v2/configure', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': aiConfig.api_key,
          'X-Base-URL': aiConfig.base_url,
          'X-Model': aiConfig.model
        },
        body: JSON.stringify({
          session_id: sessionId
        })
      });

      const result = await response.json();
      if (result.success) {
        setSessionId(result.session_id);
        return true;
      } else {
        throw new Error(result.error || '配置失败');
      }
    } catch (error) {
      console.error('配置智能体失败:', error);
      showToast(error.message, 'error');
      return false;
    }
  };

  // 发送消息
  const sendMessage = async (content) => {
    if (!content.trim() || isLoading) return;

    // 配置智能体
    const configured = await configureAgent();
    if (!configured) return;

    // 添加用户消息
    addMessage(content, MESSAGE_TYPES.USER);
    setInput('');
    setIsLoading(true);
    setIsStreaming(true);

    // 创建中止控制器
    const controller = new AbortController();
    setAbortController(controller);

    try {
      const response = await fetch('http://localhost:8000/agent/v2/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': aiConfig.api_key,
          'X-Base-URL': aiConfig.base_url,
          'X-Model': aiConfig.model
        },
        body: JSON.stringify({
          content,
          session_id: sessionId,
          user_id: 'user',
          use_enhanced: true
        }),
        signal: controller.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      // 创建流式消息
      const streamingMessage = addMessage('', MESSAGE_TYPES.ASSISTANT);
      streamingMessageRef.current = streamingMessage;

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let isJsonResponse = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;

        // 检查是否是JSON响应（错误信息）
        if (buffer.trim().startsWith('{')) {
          isJsonResponse = true;
          // 对于JSON响应，等待完整接收后再处理
          continue;
        }

        // 对于流式文本响应，实时更新
        if (!isJsonResponse) {
          updateMessage(streamingMessage.id, { content: buffer });
          scrollToBottom();
        }
      }

      // 处理最终结果
      if (isJsonResponse) {
        try {
          const result = JSON.parse(buffer);
          if (result.success === false) {
            // 显示错误信息
            updateMessage(streamingMessage.id, {
              content: `❌ ${result.error}`,
              type: MESSAGE_TYPES.ERROR
            });
          } else if (result.cards) {
            // 处理卡片数据
            const cardMessage = addMessage('', MESSAGE_TYPES.CARD, { cards: result.cards });
            processCards(result.cards);
          }
        } catch (e) {
          updateMessage(streamingMessage.id, {
            content: `❌ 响应解析错误: ${e.message}`,
            type: MESSAGE_TYPES.ERROR
          });
        }
      } else if (!buffer.trim()) {
        // 如果没有内容，显示提示
        updateMessage(streamingMessage.id, {
          content: '❌ 没有收到响应内容',
          type: MESSAGE_TYPES.ERROR
        });
      }

    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('发送消息失败:', error);
        addMessage(`发送失败: ${error.message}`, MESSAGE_TYPES.ERROR);
        showToast('发送消息失败', 'error');
      }
    } finally {
      setIsLoading(false);
      setIsStreaming(false);
      setAbortController(null);
      streamingMessageRef.current = null;
    }
  };

  // 停止生成
  const stopGeneration = () => {
    if (abortController) {
      abortController.abort();
    }
  };

  // 处理文件上传
  const handleFileUpload = async (files) => {
    const fileArray = Array.isArray(files) ? files : [files];
    
    for (const file of fileArray) {
      // 添加文件消息
      const fileMessage = addMessage('', MESSAGE_TYPES.FILE, {
        fileName: file.name,
        fileType: file.type.startsWith('image/') ? 'image' : 'pdf',
        processing: true
      });

      try {
        // 配置智能体
        const configured = await configureAgent();
        if (!configured) return;

        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('http://localhost:8000/agent/v2/upload/stream', {
          method: 'POST',
          headers: {
            'X-API-Key': aiConfig.api_key,
            'X-Base-URL': aiConfig.base_url,
            'X-Model': aiConfig.model
          },
          body: formData
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        // 更新文件消息状态
        updateMessage(fileMessage.id, { processing: false });

        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        const streamingMessage = addMessage('', MESSAGE_TYPES.ASSISTANT);

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data.trim()) {
                try {
                  const parsed = JSON.parse(data);
                  if (parsed.type === 'file_info') {
                    // 处理文件信息
                    continue;
                  }
                } catch (e) {
                  // 普通文本数据
                  buffer += data;
                  updateMessage(streamingMessage.id, { content: buffer });
                  scrollToBottom();
                }
              }
            }
          }
        }

      } catch (error) {
        console.error('文件上传失败:', error);
        updateMessage(fileMessage.id, { 
          processing: false, 
          error: true 
        });
        addMessage(`文件处理失败: ${error.message}`, MESSAGE_TYPES.ERROR);
        showToast('文件上传失败', 'error');
      }
    }

    setShowFileUpload(false);
  };

  // 清空对话
  const clearConversation = async () => {
    if (sessionId) {
      try {
        await fetch(`http://localhost:8000/agent/v2/history/${sessionId}`, {
          method: 'DELETE'
        });
      } catch (error) {
        console.error('清空对话历史失败:', error);
      }
    }
    setMessages([]);
    showToast('对话已清空');
  };

  // 重试消息
  const retryMessage = (messageId) => {
    const message = messages.find(m => m.id === messageId);
    if (message && message.type === MESSAGE_TYPES.ASSISTANT) {
      // 找到对应的用户消息并重新发送
      const messageIndex = messages.findIndex(m => m.id === messageId);
      const userMessage = messages[messageIndex - 1];
      if (userMessage && userMessage.type === MESSAGE_TYPES.USER) {
        sendMessage(userMessage.content);
      }
    }
  };

  // 编辑消息
  const editMessage = (messageId, newContent) => {
    updateMessage(messageId, { content: newContent });
    // 可以选择重新发送编辑后的消息
    sendMessage(newContent);
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* 头部 - 只在非侧边栏模式下显示 */}
      {!inSidebar && (
        <div className="flex items-center justify-between p-4 border-b bg-gray-50">
        <div className="flex items-center gap-2">
          <MessageSquare className="text-blue-600" size={20} />
          <h2 className="font-semibold">智能助手</h2>
          {sessionId && typeof sessionId === 'string' && (
            <span className="text-xs text-gray-500 bg-gray-200 px-2 py-1 rounded">
              {sessionId.slice(0, 8)}...
            </span>
          )}
        </div>
        <div className="flex items-center gap-2">
          {isStreaming && (
            <button
              onClick={stopGeneration}
              className="p-2 text-red-600 hover:bg-red-50 rounded"
              title="停止生成"
            >
              <StopCircle size={16} />
            </button>
          )}
          <button
            onClick={clearConversation}
            className="p-2 text-gray-600 hover:bg-gray-100 rounded"
            title="清空对话"
          >
            <Trash2 size={16} />
          </button>
        </div>
        </div>
      )}

      {/* 消息列表 */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
      >
        {messages.length === 0 && (
          <div className="text-center text-gray-500 mt-8">
            <MessageSquare size={48} className="mx-auto mb-4 text-gray-300" />
            <p>开始与智能助手对话</p>
            <p className="text-sm mt-2">您可以发送文本消息或上传文档</p>
          </div>
        )}
        
        {messages.map((message) => (
          <Message
            key={message.id}
            message={message}
            onRetry={retryMessage}
            onEdit={editMessage}
          />
        ))}
        
        <div ref={messagesEndRef} />
      </div>

      {/* 文件上传区域 */}
      {showFileUpload && (
        <div className="p-4 border-t bg-gray-50">
          <FileUpload
            onFileSelect={handleFileUpload}
            multiple={true}
          />
          <button
            onClick={() => setShowFileUpload(false)}
            className="mt-2 text-sm text-gray-600 hover:text-gray-800"
          >
            取消上传
          </button>
        </div>
      )}

      {/* 输入区域 */}
      <div className="p-4 border-t bg-white">
        <div className="flex items-end gap-2">
          <div className="flex-1">
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  sendMessage(input);
                }
              }}
              placeholder="输入消息... (Shift+Enter 换行)"
              className="w-full p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={1}
              style={{ minHeight: '44px', maxHeight: '120px' }}
              disabled={isLoading}
            />
          </div>
          <button
            onClick={() => setShowFileUpload(!showFileUpload)}
            className="p-3 text-gray-600 hover:bg-gray-100 rounded-lg"
            title="上传文件"
            disabled={isLoading}
          >
            <Upload size={20} />
          </button>
          <button
            onClick={() => sendMessage(input)}
            disabled={!input.trim() || isLoading}
            className="p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            title="发送消息"
          >
            {isLoading ? <Loader className="animate-spin" size={20} /> : <Send size={20} />}
          </button>
        </div>
      </div>

      {/* Toast */}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};

export default Agent;
