import React, { useState, useRef, useEffect } from 'react';
import { Send, Upload, FileText, Image, FileText as FilePdf, X as CloseIcon, RotateCcw, StopCircle, CheckCircle } from 'lucide-react';
import SubjectCard from './SubjectCard';
import AssetCard from './AssetCard';
import StaffCard from './StaffCard';
import VoucherCard from './VoucherCard';
import { Book, Layers, User, FileText as VoucherIcon } from 'lucide-react';

// 简单Toast动画提示组件
const Toast = ({ message, onClose }) => {
  useEffect(() => {
    const timer = setTimeout(onClose, 2000);
    return () => clearTimeout(timer);
  }, [onClose]);
  return (
    <div className="fixed top-6 right-6 z-50 bg-green-500 text-white px-4 py-2 rounded shadow flex items-center animate-bounce-in">
      <CheckCircle className="mr-2" size={20} />
      {message}
    </div>
  );
};

// 动画组件
const ThinkingAnimation = () => (
  <div className="flex items-center text-gray-400 animate-pulse">
    <span className="mr-2">🤔</span> 系统正在思考...
  </div>
);
const GeneratingAnimation = () => (
  <div className="flex items-center text-blue-400 animate-bounce">
    <span className="mr-2">⚙️</span> 正在生成卡片...
  </div>
);

const Agent = ({ setVouchers, setSubjects, setAssets, setStaffs, aiConfig, inSidebar = false, session, updateSession }) => {
  const [messages, setMessages] = useState(session?.messages || []);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(session?.isLoading || false);
  const isAbortedRef = useRef(false);
  const abortControllerRef = useRef(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const fileInputRef = useRef(null);
  // 预览弹窗状态 { type: 'image' | 'pdf', url: string, rotate?: number }
  const [preview, setPreview] = useState(null);
  const messagesEndRef = useRef(null);
  const [isAtBottom, setIsAtBottom] = useState(true);
  const messagesContainerRef = useRef(null);
  // 新增：多类型卡片状态
  const [pendingCards, setPendingCards] = useState([]); // [{type, data}]
  const [toast, setToast] = useState(null); // {message}
  // 新增：已确认卡片状态管理
  const [confirmedSubjects, setConfirmedSubjects] = useState([]);
  const [confirmedAssets, setConfirmedAssets] = useState([]);
  const [confirmedStaff, setConfirmedStaff] = useState([]);
  // 新增：全局科目状态
  const [allSubjects, setAllSubjects] = useState([]);
  // 初始化拉取所有科目
  useEffect(() => {
    fetch('http://localhost:8000/subjects?page=1&size=1000')
      .then(res => res.json())
      .then(data => setAllSubjects(data.subjects || []));
  }, []);

  const scrollToBottom = (force = false) => {
    if (force || isAtBottom) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 当切换到不同的会话 id 时，载入其状态
  useEffect(() => {
    setMessages(session?.messages || []);
    setIsLoading(session?.isLoading || false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session?.id]);

  // 将本地变化同步回父级 session（仅在内容变更时）
  useEffect(() => {
    if (!updateSession) return;

    const sameMsgs = session?.messages === messages;
    const sameLoading = session?.isLoading === isLoading;
    if (!sameMsgs || !sameLoading) {
      updateSession({ messages, isLoading });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [messages, isLoading]);

  // 监听滚动位置
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isBottom = Math.abs(scrollHeight - clientHeight - scrollTop) < 10;
      setIsAtBottom(isBottom);
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, []);

  // esc键关闭预览弹窗
  useEffect(() => {
    if (!preview) return;
    const handleEsc = (e) => {
      if (e.key === 'Escape') setPreview(null);
    };
    window.addEventListener('keydown', handleEsc);
    return () => window.removeEventListener('keydown', handleEsc);
  }, [preview]);

  // 检查AI配置是否完整
  const isAiConfigured = () => {
    const configured = aiConfig && aiConfig.api_key && aiConfig.base_url && aiConfig.model;
    return configured;
  };

  const handleSendMessage = async () => {
    if (!input.trim() && !selectedFile) return;
    
    if (!isAiConfigured()) {
      const errorMessage = {
        id: Date.now() + 2,
        text: 'AI服务器未配置，请先在设置中配置AI服务器',
        sender: 'error',
        timestamp: new Date().toLocaleTimeString()
      };
      setMessages(prev => [...prev, errorMessage]);
      return;
    }

    // 每次新请求前彻底重置abort状态和controller
    isAbortedRef.current = false;
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setIsLoading(true);

    const userMessage = {
      id: Date.now(),
      text: input.trim() || (selectedFile ? `已上传文件: ${selectedFile.name}` : ''),
      sender: 'user',
      timestamp: new Date().toLocaleTimeString(),
      file: selectedFile ? {
        name: selectedFile.name,
        type: selectedFile.type.startsWith('image/') ? 'image' : 
              selectedFile.type === 'application/pdf' ? 'pdf' : 'file',
        localUrl: URL.createObjectURL(selectedFile)
      } : null
    };

    // 若为新会话则自动命名
    if (updateSession && session && (session.name === '新会话' || !session.name)) {
      const cutText = (userMessage.text || '新会话').slice(0, 20);
      updateSession({ name: cutText });
    }

    setMessages(prev => [...prev, userMessage]);
    // 立即插入“思考中”动画消息
    const thinkingId = Date.now() + Math.random();
    setMessages(prev => [...prev, {
      id: thinkingId,
      type: 'thinking',
      sender: 'ai',
      timestamp: new Date().toLocaleTimeString(),
    }]);
    const messageContent = input.trim();
    setInput('');
    setSelectedFile(null);
    scrollToBottom(true);

    try {
      let response;
      
      abortControllerRef.current = new AbortController();
      const signal = abortControllerRef.current.signal;
      if (selectedFile) {
        const formData = new FormData();
        formData.append('file', selectedFile);
        if (messageContent) {
          formData.append('content', messageContent);
        }
        response = await fetch('http://localhost:8000/agent/upload', {
          method: 'POST',
          body: formData,
          headers: {
            'X-API-Key': aiConfig.api_key,
            'X-Base-URL': aiConfig.base_url,
            'X-Model': aiConfig.model
          },
          signal
        });
      } else {
        response = await fetch('http://localhost:8000/agent/message', {
          method: 'POST',
          body: JSON.stringify({ 
            content: messageContent
          }),
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': aiConfig.api_key,
            'X-Base-URL': aiConfig.base_url,
            'X-Model': aiConfig.model
          },
          signal
        });
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || '请求失败');
      }
      if (!response.body) {
        throw new Error('浏览器不支持流式读取');
      }

      // 1. 发送后立即插入“思考”动画消息
      // const thinkingId = Date.now() + Math.random();
      // setMessages(prev => [...prev, {
      //   id: thinkingId,
      //   type: 'thinking',
      //   sender: 'ai',
      //   timestamp: new Date().toLocaleTimeString(),
      // }]);
      // 2. 流式读取，缓存全部AI回复
      const reader = response.body.getReader();
      const decoder = new TextDecoder('utf-8');
      let aiText = '';
      const readStream = async () => {
        while (true) {
          if (abortControllerRef.current?.signal.aborted) break;
          const { done, value } = await reader.read();
          if (done) break;
          const chunk = decoder.decode(value, { stream: true });
          aiText += chunk;
        }
      };
      await readStream();
      // 3. 流式结束后统一判断
      let isJsonType = false;
      let cleaned = aiText.replace(/```json|```/g, '').trim();
      try {
        JSON.parse(cleaned);
        isJsonType = true;
      } catch {
        isJsonType = false;
      }
      // 4. 先移除动画消息
      setMessages(prev => prev.filter(m => m.id !== thinkingId && m.type !== 'thinking' && m.type !== 'generating'));
      if (isJsonType) {
        parseAIResponse(aiText); // parseAIResponse内部会插入卡片
      } else {
        // 插入普通文本消息
        setMessages(prev => [...prev, {
          id: Date.now() + Math.random(),
          text: aiText,
          sender: 'ai',
          timestamp: new Date().toLocaleTimeString()
        }]);
      }
    } catch (error) {
      if (error.name === 'AbortError' || abortControllerRef.current?.signal.aborted) {
        setMessages(prev => [...prev, {
          id: Date.now() + 3,
          text: '回复已中断',
          sender: 'error',
          timestamp: new Date().toLocaleTimeString()
        }]);
        // 不再弹出“处理请求时出错”
      } else {
        console.error('处理请求时出错:', error);
        const errorMessage = {
          id: Date.now() + 2,
          text: `处理请求时出错: ${error.message}`,
          sender: 'error',
          timestamp: new Date().toLocaleTimeString()
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } finally {
      setIsLoading(false);
      scrollToBottom(true);
      abortControllerRef.current = null;
    }
  };

  const handleFileClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        alert('请上传JPG、PNG图片或PDF文件');
        return;
      }
      
      if (file.size > 5 * 1024 * 1024) {
        alert('文件大小不能超过5MB');
        return;
      }
      
      setSelectedFile(file);
    }
  };

  const renderMessageIcon = (message) => {
    if (message.sender === 'user' && message.file) {
      switch (message.file.type) {
        case 'image': return <Image size={16} className="mr-2" />;
        case 'pdf': return <FilePdf size={16} className="mr-2" />;
        default: return <FileText size={16} className="mr-2" />;
      }
    }
    return null;
  };

  // 中断AI回复
  const handleAbort = () => {
    isAbortedRef.current = true;
    abortControllerRef.current?.abort();
  };

  // 卡片类型样式和图标
  const cardTypeMeta = {
    subject: { color: 'bg-blue-100 border-blue-400', icon: <Book className="text-blue-500 mr-2" size={20} /> },
    asset: { color: 'bg-green-100 border-green-400', icon: <Layers className="text-green-500 mr-2" size={20} /> },
    staff: { color: 'bg-yellow-100 border-yellow-400', icon: <User className="text-yellow-500 mr-2" size={20} /> },
    voucher: { color: 'bg-purple-100 border-purple-400', icon: <VoucherIcon className="text-purple-500 mr-2" size={20} /> },
  };

  // 卡片渲染在消息流中，样式与气泡一致
  // 在消息流中插入卡片
  const renderMessageOrCard = (message, idx) => {
    if (message.type === 'thinking') {
      return <div key={message.id} className="max-w-[85%] rounded-lg p-3 bg-gray-100 mb-2"><ThinkingAnimation /></div>;
    }
    if (message.type === 'generating') {
      return <div key={message.id} className="max-w-[85%] rounded-lg p-3 bg-blue-50 mb-2"><GeneratingAnimation /></div>;
    }
    if (message.type === 'card') {
      console.log('渲染卡片', message.cardType, message.cardProps);
      const meta = cardTypeMeta[message.cardType] || {};
      if (message.cardType === 'subject') {
        if (!message.cardProps || !message.cardProps.subject) {
          console.warn('SubjectCard props 未定义', message.cardProps);
          return null;
        }
        return (
          <div key={message.id} className={`max-w-[85%] rounded-lg p-3 border-l-4 ${meta.color} mb-2`} style={{marginLeft: message.sender === 'user' ? 'auto' : 0}}>
            <div className="flex items-center mb-2">{meta.icon}<span className="font-semibold">科目卡片</span></div>
            <SubjectCard {...message.cardProps.subject} onConfirm={data => handleCardConfirm('subject', data)} editable={!message.confirmed} />
          </div>
        );
      }
      if (message.cardType === 'asset') {
        if (!message.cardProps || !message.cardProps.asset) {
          console.warn('AssetCard props 未定义', message.cardProps);
          return null;
        }
        return (
          <div key={message.id} className={`max-w-[85%] rounded-lg p-3 border-l-4 ${meta.color} mb-2`} style={{marginLeft: message.sender === 'user' ? 'auto' : 0}}>
            <div className="flex items-center mb-2">{meta.icon}<span className="font-semibold">资产卡片</span></div>
            <AssetCard {...message.cardProps.asset} onConfirm={data => handleCardConfirm('asset', data)} editable={!message.confirmed} />
          </div>
        );
      }
      if (message.cardType === 'staff') {
        if (!message.cardProps || !message.cardProps.staff) {
          console.warn('StaffCard props 未定义', message.cardProps);
          return null;
        }
        return (
          <div key={message.id} className={`max-w-[85%] rounded-lg p-3 border-l-4 ${meta.color} mb-2`} style={{marginLeft: message.sender === 'user' ? 'auto' : 0}}>
            <div className="flex items-center mb-2">{meta.icon}<span className="font-semibold">员工卡片</span></div>
            <StaffCard {...message.cardProps.staff} onConfirm={data => handleCardConfirm('staff', data)} editable={!message.confirmed} />
          </div>
        );
      }
      if (message.cardType === 'voucher') {
        if (!message.cardProps || !message.cardProps.voucher) {
          console.warn('VoucherCard props 未定义', message.cardProps);
          return null;
        }
        return (
          <div key={message.id} className={`max-w-[85%] rounded-lg p-3 border-l-4 ${meta.color} mb-2`} style={{marginLeft: message.sender === 'user' ? 'auto' : 0}}>
            <div className="flex items-center mb-2">{meta.icon}<span className="font-semibold">凭证卡片</span></div>
            <VoucherCard {...message.cardProps.voucher} onConfirm={data => handleCardConfirm('voucher', data)} editable={!message.confirmed} />
          </div>
        );
      }
      return null;
    }
    // 普通消息
    return (
      <div
        key={message.id}
        className={`max-w-[85%] rounded-lg p-3 ${
          message.sender === 'user'
            ? 'bg-blue-500 text-white'
            : message.sender === 'error'
            ? 'bg-red-100 text-red-800'
            : 'bg-gray-100 text-gray-800'
        } mb-2`}
        style={{marginLeft: message.sender === 'user' ? 'auto' : 0}}
      >
        <div className="text-sm break-words">{message.text}</div>
        <div className="text-xs mt-1 opacity-75">{message.timestamp}</div>
      </div>
    );
  };

  function fixSubjectNameLevelByCodeLength(code, name) {
    // 4位=1级，6位=2级，8位=3级……
    const level = (code.length - 2) / 2 + 1;
    let nameParts = name.split('-');
    if (nameParts.length > level) {
      nameParts = nameParts.slice(0, level);
    }
    return nameParts.join('-');
  }

  // 卡片确认后才录入系统和推送到工作区
  const handleCardConfirm = async (type, data) => {
    // 新增：科目查重和父级校验
    if (type === 'subject') {
      // 自动补全名称层级
      data.科目名称 = fixSubjectNameLevelByCodeLength(data.科目编码, data.科目名称);
      // 1. 查重
      const checkSubjectExists = async (code) => {
        const res = await fetch(`http://localhost:8000/subjects?keyword=${code}`);
        if (!res.ok) return false;
        const result = await res.json();
        return result.subjects && result.subjects.some(s => s.科目编码 === code);
      };
      const exists = await checkSubjectExists(data.科目编码);
      if (exists) {
        setPendingCards(prev => prev.filter(card => card.data !== data));
        return;
      }
      // 2. 父级校验
      // 优化：自动补全父级编码
      let parentCode = data.父级编码;
      if (!parentCode && data.科目编码 && data.科目编码.length > 4) {
        parentCode = data.科目编码.slice(0, -2);
      }
      if (parentCode) {
        const parentExists = await checkSubjectExists(parentCode);
        if (!parentExists) {
          setMessages(prev => [...prev, { id: Date.now(), text: `父级科目${parentCode}不存在，无法创建${data.科目编码}` , sender: 'error', timestamp: new Date().toLocaleTimeString() }]);
          setPendingCards(prev => prev.filter(card => card.data !== data));
          return;
        }
      }
    }
    let url = '';
    let method = 'POST';
    if (type === 'subject') url = 'http://localhost:8000/subjects';
    if (type === 'asset') url = 'http://localhost:8000/assets';
    if (type === 'staff') url = 'http://localhost:8000/staffs';
    if (type === 'voucher') url = null; // 仅推送到工作区，不录入
    if (!url && type !== 'voucher') return;
    // 科目卡片字段严格对齐
    let submitData = data;
    if (type === 'subject') {
      submitData = mapSubjectFields(data);
    }
    // 资产卡片字段转换为英文字段名以匹配后端API
    if (type === 'asset') {
      submitData = {
        code: data.资产编码 || '',
        name: data.资产名称 || '',
        category: data.类别 || '',
        original_value: parseFloat(data.原值) || 0,
        net_value: parseFloat(data.净值) || 0,
        purchase_date: data.购置日期 || '',
        useful_life: parseInt(data.使用年限) || 0,
        status: data.状态 || '',
        remark: data.备注 || ''
      };
    }
    try {
      if (type !== 'voucher') {
        const res = await fetch(url, {
          method,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(submitData)
        });
        if (!res.ok) throw new Error('录入失败');
        // 新增：录入成功后刷新allSubjects
        if (type === 'subject') {
          fetch('http://localhost:8000/subjects?page=1&size=1000')
            .then(res => res.json())
            .then(data => setAllSubjects(data.subjects || []));
        }
      }
      // 只有确认后才推送到工作区
      if (type === 'voucher' && setVouchers) setVouchers(prev => [...prev, data]);
      if (type === 'subject' && setSubjects) setSubjects(prev => [...prev, data]);
      if (type === 'asset' && setAssets) setAssets(prev => [...prev, data]);
      if (type === 'staff' && setStaffs) setStaffs(prev => [...prev, data]);
      setPendingCards(prev => prev.filter(card => card.data !== data));
      
      // 根据类型显示中文提示信息
      const typeNames = {
        'subject': '科目',
        'asset': '资产', 
        'staff': '员工',
        'voucher': '凭证'
      };
      const typeName = typeNames[type] || type;
      setMessages(prev => [...prev, { id: Date.now(), text: `${typeName}录入成功`, sender: 'system', timestamp: new Date().toLocaleTimeString() }]);
      
      // 更新已确认卡片状态
      if (type === 'subject') {
        setConfirmedSubjects(prev => [...prev, data]);
      } else if (type === 'asset') {
        setConfirmedAssets(prev => [...prev, data]);
      } else if (type === 'staff') {
        setConfirmedStaff(prev => [...prev, data]);
      }

      // 新增：将消息流中对应卡片的 confirmed 设为 true
      setMessages(prev => prev.map(msg => {
        if (msg.type === 'card' && msg.cardType === type && JSON.stringify(msg.cardProps[type]) === JSON.stringify(data)) {
          return { ...msg, confirmed: true };
        }
        return msg;
      }));
    } catch (e) {
      // 根据类型显示中文错误提示信息
      const typeNames = {
        'subject': '科目',
        'asset': '资产', 
        'staff': '员工',
        'voucher': '凭证'
      };
      const typeName = typeNames[type] || type;
      setMessages(prev => [...prev, { id: Date.now(), text: `${typeName}录入失败: ${e.message}`, sender: 'error', timestamp: new Date().toLocaleTimeString() }]);
    }
  };

  // 卡片有效性判断
  const isValidCard = (type, data) => {
    if (!data) return false;
    if (type === 'subject') {
      return !!(data['科目名称'] || data['name']);
    }
    if (type === 'asset') {
      return !!(data['资产名称'] || data['name']);
    }
    if (type === 'staff') {
      return !!(data['姓名'] || data['name']);
    }
    if (type === 'voucher') {
      // 只要有借方和贷方数组且有金额即可
      return Array.isArray(data['借方']) && data['借方'].length > 0 &&
             Array.isArray(data['贷方']) && data['贷方'].length > 0;
    }
    return false;
  };

  // 字段映射函数（全部用中文字段名）
  const mapSubjectFields = (data) => ({
    科目编码: data.科目编码 || '',
    科目名称: data.科目名称 || '',
    类别: data.类别 || '',
    方向: data.方向 || '',
    备注: data.备注 || '',
    级次: data.级次 || 1,
    父级编码: data.父级编码 === undefined ? null : (data.父级编码 || null),
    辅助核算: Array.isArray(data.辅助核算) ? data.辅助核算 : [],
    末级: data.末级 !== undefined ? data.末级 : true,
    状态: data.状态 || '启用',
    数量核算: data.数量核算 !== undefined ? data.数量核算 : false
  });
  const mapVoucherFields = (data) => ({
    日期: data.日期 || '',
    摘要: data.摘要 || '',
    借方: Array.isArray(data.借方) ? data.借方 : [],
    贷方: Array.isArray(data.贷方) ? data.贷方 : []
  });
  const mapAssetFields = (data) => ({
    资产编码: data.资产编码 || '',
    资产名称: data.资产名称 || '',
    类别: data.类别 || '',
    原值: parseFloat(data.原值) || 0,
    净值: parseFloat(data.净值) || 0,
    购置日期: data.购置日期 || '',
    使用年限: parseInt(data.使用年限) || 0,
    状态: data.状态 || '',
    备注: data.备注 || ''
  });
  const mapStaffFields = (data) => ({
    工号: data.工号 || '',
    姓名: data.姓名 || '',
    岗位编码: data.岗位编码 || '',
    电话: data.电话 || '',
    状态: data.状态 || '',
    备注: data.备注 || ''
  });

  // 检查凭证卡片是否可以创建（依赖科目和资产）
  const canCreateVoucher = (voucherData) => {
    if (!voucherData.借方 || !voucherData.贷方) {
      console.log('凭证数据缺少借贷方', voucherData);
      return false;
    }
    
    // 检查借方科目是否已确认
    const debitSubjects = voucherData.借方.map(entry => entry.科目编码).filter(Boolean);
    const confirmedSubjectCodes = confirmedSubjects.map(s => s.科目编码);
    const allDebitSubjectsConfirmed = debitSubjects.every(code => confirmedSubjectCodes.includes(code));
    
    // 检查贷方科目是否已确认
    const creditSubjects = voucherData.贷方.map(entry => entry.科目编码).filter(Boolean);
    const allCreditSubjectsConfirmed = creditSubjects.every(code => confirmedSubjectCodes.includes(code));
    
    console.log('凭证依赖检查:', {
      debitSubjects,
      creditSubjects,
      confirmedSubjectCodes,
      allDebitSubjectsConfirmed,
      allCreditSubjectsConfirmed
    });
    
    // 如果没有任何科目编码，也允许创建（可能是新科目）
    if (debitSubjects.length === 0 && creditSubjects.length === 0) {
      console.log('凭证没有科目编码，允许创建');
      return true;
    }
    
    // 如果没有任何已确认的科目，也允许创建（可能是第一次创建）
    if (confirmedSubjectCodes.length === 0) {
      console.log('没有已确认的科目，允许创建凭证');
      return true;
    }
    
    // 如果有科目编码但未确认，也允许创建（用户可以先确认凭证，再确认科目）
    if (debitSubjects.length > 0 || creditSubjects.length > 0) {
      console.log('有科目编码但未确认，允许创建凭证');
      return true;
    }
    
    return allDebitSubjectsConfirmed && allCreditSubjectsConfirmed;
  };

  // 修改parseAIResponse，按顺序处理卡片
  const parseAIResponse = (aiText) => {
    let cleaned = aiText.replace(/```json|```/g, '').trim();
    cleaned = cleaned
      .replace(/([,{\s])'([^']+)'\s*:/g, '$1"$2":')
      .replace(/: '([^']*)'/g, ': "$1"');
    try {
      const obj = JSON.parse(cleaned);
      console.log('AI原始返回:', aiText);
      let foundCard = false;
      const newMessages = [];
      
      // 按顺序处理：科目 -> 资产 -> 员工 -> 凭证
      const processOrder = ['subject', 'asset', 'staff', 'voucher'];
      
      // 新增：查重函数
      const checkSubjectExistsSync = (code) => {
        return allSubjects.some(s => s.科目编码 === code);
      };
      const handleItem = (item) => {
        console.log('处理卡片项:', item);
        const { type, op = 'create', data } = item;
        if (op === 'create' && isValidCard(type, data)) {
          // 科目卡片查重，已存在则跳过
          if (type === 'subject' && checkSubjectExistsSync(data.科目编码)) {
            return;
          }
          // 检查凭证依赖关系
          if (type === 'voucher' && !canCreateVoucher(data)) {
            console.warn('凭证卡片依赖的科目未确认，跳过创建');
            return;
          }
          
          let mapped =
            type === 'subject' ? mapSubjectFields(data) :
            type === 'voucher' ? mapVoucherFields(data) :
            type === 'asset' ? mapAssetFields(data) :
            type === 'staff' ? mapStaffFields(data) :
            data;
          console.log('字段映射结果', type, mapped);
          if (mapped && typeof mapped === 'object' && Object.keys(mapped).length > 0) {
            newMessages.push({
              id: Date.now() + Math.random(),
              type: 'card',
              cardType: type,
              cardProps: { [type]: mapped },
              sender: 'ai',
              confirmed: false // 新增
            });
            foundCard = true;
          } else {
            console.warn('字段映射为空，未插入卡片', type, data);
          }
        }
      };
      
      if (Array.isArray(obj)) {
        // 按顺序处理数组中的卡片
        const sortedItems = obj.sort((a, b) => {
          const aIndex = processOrder.indexOf(a.type);
          const bIndex = processOrder.indexOf(b.type);
          return aIndex - bIndex;
        });
        sortedItems.forEach(handleItem);
      } else {
        // 按顺序处理对象中的卡片
        processOrder.forEach(type => {
          if (obj[type]) {
            const op = obj[type].op || 'create';
            const data = obj[type].data;
            if (op === 'create') {
              if (type === 'subject' && Array.isArray(data)) {
                data.forEach(item => {
                  if (isValidCard(type, item) && !checkSubjectExistsSync(item.科目编码)) {
                    newMessages.push({
                      id: Date.now() + Math.random(),
                      type: 'card',
                      cardType: type,
                      cardProps: { subject: mapSubjectFields(item) },
                      sender: 'ai',
                      confirmed: false
                    });
                    foundCard = true;
                  }
                });
              } else if (isValidCard(type, data)) {
                // 检查凭证依赖关系
                if (type === 'voucher' && !canCreateVoucher(data)) {
                  console.warn('凭证卡片依赖的科目未确认，跳过创建');
                  return;
                }
                
                newMessages.push({
                  id: Date.now() + Math.random(),
                  type: 'card',
                  cardType: type,
                  cardProps: {
                    [type]:
                      type === 'subject' ? mapSubjectFields(data) :
                      type === 'voucher' ? mapVoucherFields(data) :
                      type === 'asset' ? mapAssetFields(data) :
                      type === 'staff' ? mapStaffFields(data) :
                      data
                  },
                  sender: 'ai',
                  confirmed: false
                });
                foundCard = true;
              }
            }
          }
        });
      }
      
      if (foundCard) {
        console.log('最终插入的卡片:', newMessages);
        setMessages(prev => {
          let filtered = prev.filter(m => m.type !== 'thinking' && m.type !== 'generating');
          return [...filtered, ...newMessages];
        });
        setToast('AI已生成卡片，请确认');
        return true;
      }
      if (obj.type === 'message' || obj.type === 'confirm') {
        setMessages(prev => [...prev, { id: Date.now(), text: obj.data?.text || cleaned, sender: 'ai', timestamp: new Date().toLocaleTimeString() }]);
        return false;
      }
      setToast('未识别到有效卡片');
      return false;
    } catch (e) {
      setMessages(prev => [...prev, { id: Date.now(), text: aiText, sender: 'ai', timestamp: new Date().toLocaleTimeString() }]);
      return false;
    }
  };

  return (
    <>
    {toast && <Toast message={toast} onClose={() => setToast(null)} />}
    <div className="flex flex-col h-full">
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto px-4"
      >
        <div className="space-y-4 py-4">
          {(() => { console.log('渲染消息流:', messages); return null; })()}
          {messages.map((msg, idx) => renderMessageOrCard(msg, idx))}
          <div ref={messagesEndRef} />
        </div>
      </div>
      
      <div className="relative border-t border-gray-200 px-2 py-3">
        <div className="flex space-x-2">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".jpg,.jpeg,.png,.pdf"
            className="hidden"
          />
          <button
            onClick={handleFileClick}
            className="w-10 h-10 flex items-center justify-center bg-gray-100 text-gray-500 hover:bg-gray-200 hover:text-gray-700 rounded-lg transition-colors"
            title="上传文件"
          >
            <Upload size={20} />
          </button>
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage()}
            placeholder="输入消息..."
            className="flex-1 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            onClick={isLoading ? handleAbort : handleSendMessage}
            disabled={isLoading ? false : (!input.trim() && !selectedFile)}
            className={`w-10 h-10 flex items-center justify-center rounded-lg transition-colors ${
              isLoading
                ? 'bg-red-500 text-white hover:bg-red-600'
                : (!input.trim() && !selectedFile)
                  ? 'bg-gray-100 text-gray-400'
                  : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
            title={isLoading ? '中断回复' : '发送'}
          >
            {isLoading ? <StopCircle size={22} /> : <Send size={20} />}
          </button>
        </div>
        {selectedFile && (
          <div className="mt-2 text-sm text-gray-500 flex items-center">
            <FileText size={16} className="mr-1" />
            {selectedFile.name}
          </div>
        )}
      </div>
      {/* 新增：多类型卡片渲染区 */}
      {pendingCards.length > 0 && (
        <div className="space-y-2 mb-4">
          {pendingCards.map((card, idx) => {
            if (card.type === 'subject') {
              return <SubjectCard key={idx} subject={card.data} onConfirm={data => handleCardConfirm('subject', data)} />;
            }
            if (card.type === 'asset') {
              return <AssetCard key={idx} asset={card.data} onConfirm={data => handleCardConfirm('asset', data)} />;
            }
            if (card.type === 'staff') {
              return <StaffCard key={idx} staff={card.data} onConfirm={data => handleCardConfirm('staff', data)} />;
            }
            return null;
          })}
        </div>
      )}
    </div>
    {/* -------- 预览弹窗 ---------- */}
    {preview && (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75" onClick={() => setPreview(null)}>
        <div className="relative max-h-[90%] max-w-[90%]" onClick={(e) => e.stopPropagation()}>
          {/* 关闭按钮始终在最上层 */}
          <button
            onClick={() => setPreview(null)}
            className="fixed top-8 right-8 z-50 text-white hover:text-gray-300 bg-black bg-opacity-40 rounded-full p-1"
            style={{ pointerEvents: 'auto' }}
          >
            <CloseIcon size={28} />
          </button>
          {preview.type === 'image' ? (
            <>
              <img
                src={preview.url}
                alt="预览"
                className="max-h-[80vh] max-w-[90vw] rounded shadow-lg block mx-auto"
                style={{ transform: `rotate(${preview.rotate}deg)` }}
              />
              <button
                onClick={() => setPreview((prev) => ({ ...prev, rotate: (prev.rotate + 90) % 360 }))}
                className="absolute bottom-4 right-4 text-white bg-gray-700 bg-opacity-60 hover:bg-opacity-80 p-2 rounded-full"
              >
                <RotateCcw size={20} />
              </button>
            </>
          ) : (
            <iframe
              src={preview.url}
              title="PDF预览"
              className="w-[80vw] h-[80vh] bg-white rounded shadow-lg"
            />
          )}
        </div>
      </div>
    )}
    </>
  );
};

export default Agent;
