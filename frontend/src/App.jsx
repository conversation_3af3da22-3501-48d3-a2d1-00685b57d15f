import React, { useState, useEffect } from 'react'
import FunctionBar from './components/FunctionBar'
import Sidebar from './components/Sidebar'
import Workspace from './components/Workspace'
import Agent from './components/Agent'
import Resizer from './components/Resizer'
import ConversationList from './components/ConversationList'
import Voucher from './components/Voucher'
import Bookkeeping from './components/Bookkeeping'
import Report from './components/Report'
import Settlement from './components/Settlement'
import Asset from './components/Asset'
import Invoice from './components/Invoice'
import Cashier from './components/Cashier'
import Salary from './components/Salary'
import Tax from './components/Tax'
import SettingsDialog from './components/SettingsDialog'
import SubjectTable from './components/SubjectTable';
import RoleStaffConfig from './components/RoleStaffConfig';

const App = () => {
  const [activeFeature, setActiveFeature] = useState('agent')
  const [vouchers, setVouchers] = useState([])
  const [subjects, setSubjects] = useState([])
  const [assets, setAssets] = useState([])
  const [staffs, setStaffs] = useState([])
  const [showSettings, setShowSettings] = useState(false)

  // Agent侧边栏宽度和会话管理
  const [agentWidth, setAgentWidth] = useState(380)
  const [isResizing, setIsResizing] = useState(false)
  const [sessions, setSessions] = useState(() => [
    { id: Date.now(), name: '新会话', messages: [], isLoading: false }
  ])
  const [activeSessionId, setActiveSessionId] = useState(sessions[0].id)

  // 会话管理函数
  const createSession = () => {
    const newSession = { id: Date.now(), name: '新会话', messages: [], isLoading: false }
    setSessions(prev => [...prev, newSession])
    setActiveSessionId(newSession.id)
  }

  const updateSession = (id, updates) => {
    setSessions(prev => prev.map(s => s.id === id ? { ...s, ...updates } : s))
  }

  // 处理Agent区域大小调整
  const handleAgentResize = (clientX) => {
    setIsResizing(true)
    const newWidth = clientX - 64 // 考虑左侧功能栏的宽度64px
    const constrainedWidth = Math.max(320, Math.min(newWidth, window.innerWidth * 0.7))
    setAgentWidth(constrainedWidth)
  }

  const [aiConfig, setAiConfig] = useState(() => {
    const savedConfig = localStorage.getItem('aiConfig')
    return savedConfig 
      ? JSON.parse(savedConfig)
      : {
          api_key: '',
          base_url: 'https://aistudio.baidu.com/llm/lmapi/v3',
          model: 'ernie-4.5-turbo-vl-preview'
        }
  })

  // 保存配置到本地存储和状态
  const saveConfig = (config) => {
    setAiConfig(config)
    localStorage.setItem('aiConfig', JSON.stringify(config))
  }

  // 测试AI服务器连接
  const testAiConnection = async (config) => {
    try {
      // 先保存配置
      saveConfig(config)
      
      const response = await fetch('http://localhost:8000/ai/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || '连接测试失败')
      }
      
      return await response.json()
    } catch (error) {
      throw new Error(error.message)
    }
  }

  const features = {
    agent: { name: '智能体', component: Agent },
    voucher: { name: '凭证', component: Voucher },
    bookkeeping: { name: '账簿', component: Bookkeeping },
    report: { name: '报表', component: Report },
    settlement: { name: '结算', component: Settlement },
    asset: { name: '资产', component: Asset },
    invoice: { name: '发票', component: Invoice },
    cashier: { name: '出纳', component: Cashier },
    salary: { name: '工资', component: Salary },
    tax: { name: '税务', component: Tax },
    subject: { name: '科目表', component: SubjectTable },
    roleStaff: { name: '岗位人员', component: RoleStaffConfig },
  }

  const ActiveComponent = features[activeFeature].component

  return (
    <div className="flex h-screen bg-gray-100">
      <FunctionBar
        activeFeature={activeFeature}
        onFeatureSelect={setActiveFeature}
        features={features}
        onShowSettings={() => setShowSettings(true)}
      />
      {activeFeature === 'agent' ? (
        <>
          <div
            className="relative bg-white border-r border-gray-200 flex flex-col h-full"
            style={{ width: `${agentWidth}px`, transition: isResizing ? 'none' : 'width 0.3s ease' }}
            onMouseUp={() => setIsResizing(false)}
          >
            <div className="border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-800 p-4">智能助手</h2>
              <ConversationList
                sessions={sessions}
                activeId={activeSessionId}
                onSelect={setActiveSessionId}
                onNew={createSession}
              />
            </div>
            <div className="flex-1 overflow-hidden">
              <div className="h-full">
                {sessions.map(session => (
                  <div key={session.id} className={`${session.id === activeSessionId ? 'h-full' : 'hidden'}`} style={{height:'100%'}}>
                    <Agent
                      aiConfig={aiConfig}
                      setVouchers={setVouchers}
                      setSubjects={setSubjects}
                      setAssets={setAssets}
                      setStaffs={setStaffs}
                      inSidebar={true}
                      session={session}
                      updateSession={updates => updateSession(session.id, updates)}
                    />
                  </div>
                ))}
              </div>
            </div>
            <Resizer onResize={handleAgentResize} />
          </div>
          <Workspace vouchers={vouchers} subjects={subjects} assets={assets} staffs={staffs} />
        </>
      ) : (
        <>
          <Sidebar
            activeFeature={activeFeature}
            features={features}
            aiConfig={aiConfig}
            setVouchers={setVouchers}
            setSubjects={setSubjects}
            setAssets={setAssets}
            setStaffs={setStaffs}
          />
          <div className="flex-1 flex flex-col h-full overflow-hidden bg-gray-100">
            <div className="flex-1 overflow-auto p-6">
              <ActiveComponent vouchers={vouchers} setVouchers={setVouchers} aiConfig={aiConfig} />
            </div>
          </div>
        </>
      )}
      <SettingsDialog
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        onSave={(config) => {
          saveConfig(config)
          setShowSettings(false)
        }}
        onTest={testAiConnection}
        initialConfig={aiConfig}
      />
    </div>
  )
}

export default App
