import React, { useState, useEffect } from 'react'
import FunctionBar from './components/FunctionBar'
import Sidebar from './components/Sidebar'
import Workspace from './components/Workspace'
import Agent from './components/Agent'
import EnhancedAgent from './components/EnhancedAgent'
import Voucher from './components/Voucher'
import Bookkeeping from './components/Bookkeeping'
import Report from './components/Report'
import Settlement from './components/Settlement'
import Asset from './components/Asset'
import Invoice from './components/Invoice'
import Cashier from './components/Cashier'
import Salary from './components/Salary'
import Tax from './components/Tax'
import SettingsDialog from './components/SettingsDialog'
import SubjectTable from './components/SubjectTable';
import RoleStaffConfig from './components/RoleStaffConfig';

const App = () => {
  const [activeFeature, setActiveFeature] = useState('agent')
  const [vouchers, setVouchers] = useState([])
  const [subjects, setSubjects] = useState([])
  const [assets, setAssets] = useState([])
  const [staffs, setStaffs] = useState([])
  const [showSettings, setShowSettings] = useState(false)
  const [aiConfig, setAiConfig] = useState(() => {
    const savedConfig = localStorage.getItem('aiConfig')
    return savedConfig 
      ? JSON.parse(savedConfig)
      : {
          api_key: '',
          base_url: 'https://aistudio.baidu.com/llm/lmapi/v3',
          model: 'ernie-4.5-turbo-vl-preview'
        }
  })

  // 保存配置到本地存储和状态
  const saveConfig = (config) => {
    setAiConfig(config)
    localStorage.setItem('aiConfig', JSON.stringify(config))
  }

  // 测试AI服务器连接
  const testAiConnection = async (config) => {
    try {
      // 先保存配置
      saveConfig(config)
      
      const response = await fetch('http://localhost:8000/ai/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      })
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.detail || '连接测试失败')
      }
      
      return await response.json()
    } catch (error) {
      throw new Error(error.message)
    }
  }

  const features = {
    agent: { name: '智能体', component: EnhancedAgent },
    voucher: { name: '凭证', component: Voucher },
    bookkeeping: { name: '账簿', component: Bookkeeping },
    report: { name: '报表', component: Report },
    settlement: { name: '结算', component: Settlement },
    asset: { name: '资产', component: Asset },
    invoice: { name: '发票', component: Invoice },
    cashier: { name: '出纳', component: Cashier },
    salary: { name: '工资', component: Salary },
    tax: { name: '税务', component: Tax },
    subject: { name: '科目表', component: SubjectTable },
    roleStaff: { name: '岗位人员', component: RoleStaffConfig },
  }

  const ActiveComponent = features[activeFeature].component

  return (
    <div className="flex h-screen bg-gray-100">
      <FunctionBar
        activeFeature={activeFeature}
        onFeatureSelect={setActiveFeature}
        features={features}
        onShowSettings={() => setShowSettings(true)}
      />
      <Sidebar
        activeFeature={activeFeature}
        features={features}
        aiConfig={aiConfig}
        setVouchers={setVouchers}
        setSubjects={setSubjects}
        setAssets={setAssets}
        setStaffs={setStaffs}
      />
      {activeFeature === 'agent' ? (
        <div className="flex-1 flex flex-col h-full overflow-hidden">
          <EnhancedAgent
            setVouchers={setVouchers}
            setSubjects={setSubjects}
            setAssets={setAssets}
            setStaffs={setStaffs}
            aiConfig={aiConfig}
          />
        </div>
      ) : (
        <div className="flex-1 flex flex-col h-full overflow-hidden bg-gray-100">
          <div className="flex-1 overflow-auto p-6">
            <ActiveComponent vouchers={vouchers} setVouchers={setVouchers} aiConfig={aiConfig} />
          </div>
        </div>
      )}
      <SettingsDialog
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        onSave={(config) => {
          saveConfig(config)
          setShowSettings(false)
        }}
        onTest={testAiConnection}
        initialConfig={aiConfig}
      />
    </div>
  )
}

export default App
