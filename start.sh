#!/bin/bash

# 设置颜色变量
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 检查是否在项目根目录
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo -e "${RED}错误：请在项目根目录下运行此脚本${NC}"
    exit 1
fi

# 启动函数
start_project() {
    echo -e "${GREEN}正在启动会计应用...${NC}"
    
    # 启动Python虚拟环境
    if [ ! -d "venv" ]; then
        echo -e "${YELLOW}警告：未找到虚拟环境，正在创建...${NC}"
        python -m venv venv
    fi
    
    # 激活虚拟环境并获取Python和uvicorn的路径
    source venv/bin/activate
    PYTHON_PATH=$(which python)
    UVICORN_PATH=$(which uvicorn)
    
    # 安装Python依赖（如果不存在）
    if [ ! -f "backend/requirements.txt" ]; then
        echo -e "${YELLOW}警告：requirements.txt不存在，正在创建...${NC}"
        pip install fastapi uvicorn python-multipart aiohttp Pillow PyPDF2
        pip freeze > backend/requirements.txt
    else
        # 确保所有依赖都已安装
        echo -e "${GREEN}正在安装Python依赖...${NC}"
        pip install -r backend/requirements.txt

        # 检查可选依赖
        echo -e "${GREEN}检查可选依赖...${NC}"

        # 检查LangChain相关依赖
        if ! pip show langchain >/dev/null 2>&1; then
            echo -e "${YELLOW}警告：LangChain未安装，智能体增强功能将不可用${NC}"
        else
            echo -e "${GREEN}✓ LangChain已安装${NC}"
        fi

        # 检查网络搜索依赖
        if ! pip show duckduckgo-search >/dev/null 2>&1; then
            echo -e "${YELLOW}警告：duckduckgo-search未安装，网络搜索功能将使用模拟数据${NC}"
        else
            echo -e "${GREEN}✓ 网络搜索功能可用${NC}"
        fi

        # 检查向量数据库依赖
        if ! pip show chromadb >/dev/null 2>&1; then
            echo -e "${YELLOW}警告：chromadb未安装，向量存储功能将不可用${NC}"
        else
            echo -e "${GREEN}✓ 向量数据库功能可用${NC}"
        fi
    fi
    
    # 启动后端服务
    echo -e "${GREEN}正在启动后端服务...${NC}"
    cd backend
    $PYTHON_PATH $UVICORN_PATH main:app --reload --port 8000 &
    BACKEND_PID=$!
    cd ..
    
    # 启动前端服务
    echo -e "${GREEN}正在启动前端服务...${NC}"
    cd frontend
    if [ ! -f "yarn.lock" ]; then
        echo -e "${YELLOW}警告：前端依赖未安装，正在安装...${NC}"
        yarn install
    fi
    yarn dev &
    FRONTEND_PID=$!
    cd ..
    
    # 添加清理函数
    cleanup() {
        echo -e "${YELLOW}正在停止服务...${NC}"
        if kill -0 $BACKEND_PID 2>/dev/null; then
            kill $BACKEND_PID
        fi
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            kill $FRONTEND_PID
        fi
        echo -e "${GREEN}服务已停止${NC}"
        exit 0
    }
    
    # 捕获Ctrl+C信号
    trap cleanup INT TERM
    
    # 等待进程结束
    wait $BACKEND_PID
    wait $FRONTEND_PID
}

# 主程序
case "$1" in
    start)
        start_project
        ;;
    *)
        echo "用法: $0 {start}"
        exit 1
esac

exit 0
