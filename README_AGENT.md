# 智能体系统重构说明

## 概述

本项目已成功使用LangChain等技术重构了智能体部分，实现了提示管理、链式调用、记忆系统、工具集成、网络搜索、文档处理等特性，并支持人工干预和多文档上传。

## 架构设计

### 核心模块

```
backend/core/agent/
├── __init__.py          # 模块导出
├── agent.py            # 智能体执行器
├── llm.py              # LLM管理器
├── memory.py           # 记忆系统
├── prompts.py          # 提示管理
├── chains.py           # 链式调用
├── tools.py            # 工具系统
├── external_tools.py   # 外部工具集成
└── monitoring.py       # 性能监控
```

### API路由

- **统一路由**: `backend/route/agent.py`
- **向后兼容**: 保留原有API端点
- **增强功能**: 新增v2版本API端点

### 前端组件

- **增强智能体**: `frontend/src/components/EnhancedAgent.jsx`
- **多文档上传**: 支持拖拽上传多个文件
- **实时对话**: 类似Augment/Cline的对话体验
- **人工干预**: 支持消息编辑和重试

## 主要功能

### 1. 提示管理系统

- **模板化管理**: 支持动态提示模板
- **变量替换**: 自动填充上下文变量
- **版本控制**: 支持模板导入导出

### 2. 链式调用引擎

- **凭证生成链**: 智能生成会计凭证
- **文档分析链**: 分析PDF和图片文档
- **对话总结链**: 自动总结对话内容

### 3. 记忆系统

- **对话记忆**: 维护会话历史
- **上下文记忆**: 存储业务数据
- **会话管理**: 支持多会话并发

### 4. 工具集成

#### 内置工具
- **PDF处理**: 提取PDF文档文本
- **OCR识别**: 图片文字识别
- **数据库查询**: 查询科目、资产、员工信息
- **凭证创建**: 生成会计凭证

#### 外部工具
- **网络搜索**: DuckDuckGo搜索引擎
- **网页抓取**: 获取网页内容
- **天气查询**: OpenWeather API

### 5. 性能监控

- **执行时间**: 监控各操作耗时
- **错误率**: 统计错误发生率
- **性能警报**: 自动检测性能问题
- **缓存管理**: 智能缓存机制

## API接口

### 原有API（向后兼容）

```
POST /ai/test                    # 测试AI连接
POST /agent/message              # 发送消息
POST /agent/upload               # 上传文件
```

### 增强API（v2版本）

```
POST /agent/v2/configure         # 配置智能体
POST /agent/v2/test              # 测试连接
POST /agent/v2/message           # 发送消息
POST /agent/v2/stream            # 流式消息
POST /agent/v2/upload            # 上传文档
POST /agent/v2/upload/stream     # 流式上传
POST /agent/v2/tool              # 执行工具
GET  /agent/v2/history/{id}      # 获取历史
DELETE /agent/v2/history/{id}    # 清空历史
GET  /agent/v2/tools             # 列出工具
GET  /agent/v2/performance       # 性能指标
DELETE /agent/v2/performance/alerts # 清空警报
```

## 配置说明

### 环境变量

创建 `.env` 文件（参考 `backend/.env.example`）：

```bash
# 网络搜索API密钥（可选）
SERPER_API_KEY=your_serper_api_key

# 天气API密钥（可选）
OPENWEATHER_API_KEY=your_openweather_api_key

# 数据库配置
DATABASE_URL=sqlite:///./app.db

# 向量数据库配置
CHROMA_PERSIST_DIRECTORY=./chroma_db

# 日志级别
LOG_LEVEL=INFO
```

### 依赖安装

```bash
# 核心依赖
pip install -r backend/requirements.txt

# 可选依赖（增强功能）
pip install duckduckgo-search    # 网络搜索
pip install beautifulsoup4       # 网页解析
pip install chromadb            # 向量数据库
pip install unstructured        # 文档加载器
```

## 启动方式

### 开发环境

```bash
# 使用启动脚本
./start.sh start

# 或手动启动
cd backend && python -m uvicorn main:app --reload
cd frontend && yarn dev
```

### 生产环境

```bash
# 后端
cd backend && python -m uvicorn main:app --host 0.0.0.0 --port 8000

# 前端
cd frontend && yarn build && yarn start
```

## 测试

```bash
# 运行测试
cd backend && python -m pytest tests/test_agent.py -v

# 或使用测试脚本
cd backend && python tests/test_agent.py
```

## 使用示例

### 基础对话

```javascript
// 发送消息
const response = await fetch('/agent/v2/stream', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    content: "帮我生成一张现金收入凭证",
    session_id: "session_123",
    use_enhanced: true
  })
});
```

### 文档上传

```javascript
// 上传文档
const formData = new FormData();
formData.append('file', file);

const response = await fetch('/agent/v2/upload/stream', {
  method: 'POST',
  body: formData
});
```

### 工具调用

```javascript
// 执行工具
const response = await fetch('/agent/v2/tool', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    tool_name: "web_search",
    parameters: {
      query: "会计准则",
      max_results: 5
    }
  })
});
```

## 扩展开发

### 添加新工具

1. 继承 `BaseAgentTool` 类
2. 实现 `execute` 方法
3. 注册到工具注册表

```python
class CustomTool(BaseAgentTool):
    name = "custom_tool"
    description = "自定义工具"
    
    async def execute(self, **kwargs):
        # 实现工具逻辑
        return {"result": "success"}

# 注册工具
register_tool(CustomTool())
```

### 添加新链式调用

1. 继承 `BaseChain` 类
2. 实现 `build` 方法
3. 注册到链管理器

```python
class CustomChain(BaseChain):
    def build(self, llm, **kwargs):
        # 构建链式调用
        self.chain = prompt | llm | parser
```

## 注意事项

1. **API兼容性**: v2 API与原有API并存，确保向后兼容
2. **性能监控**: 生产环境建议启用性能监控
3. **错误处理**: 所有API都有完善的错误处理机制
4. **安全性**: 敏感信息通过环境变量配置
5. **扩展性**: 模块化设计，便于后续扩展

## 故障排除

### 常见问题

1. **LangChain未安装**: 检查依赖安装
2. **API密钥配置**: 检查环境变量设置
3. **文件上传失败**: 检查文件格式和大小
4. **性能问题**: 查看性能监控指标

### 日志查看

```bash
# 查看应用日志
tail -f backend/app.log

# 查看性能指标
curl http://localhost:8000/agent/v2/performance
```

## 更新日志

- **v2.0.0**: 完整重构智能体系统
- **v2.1.0**: 添加性能监控和缓存
- **v2.2.0**: 集成外部工具和网络搜索
- **v2.3.0**: 优化前端用户体验
