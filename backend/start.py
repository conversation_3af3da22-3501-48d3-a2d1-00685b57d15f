#!/usr/bin/env python3
"""
LangChain智能体应用启动脚本
"""

import os
import sys
import logging
import uvicorn
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入配置
from core.config import config

def setup_environment():
    """设置环境"""
    # 创建必要的目录
    config.get_upload_dir()
    config.get_chroma_dir()
    
    # 设置日志
    config.setup_logging()
    
    logger = logging.getLogger(__name__)
    logger.info("环境设置完成")
    
    # 检查可选依赖
    optional_deps = {
        "duckduckgo_search": "网络搜索功能",
        "beautifulsoup4": "网页内容提取",
        "chromadb": "向量数据库",
        "unstructured": "文档加载器"
    }
    
    for dep, description in optional_deps.items():
        try:
            __import__(dep)
            logger.info(f"✓ {dep} 已安装 - {description}")
        except ImportError:
            logger.warning(f"✗ {dep} 未安装 - {description} 将不可用")
    
    # 检查API密钥
    if config.SERPER_API_KEY:
        logger.info("✓ Serper API密钥已配置 - 网络搜索功能可用")
    else:
        logger.info("✗ Serper API密钥未配置 - 将使用模拟搜索结果")
    
    if config.OPENWEATHER_API_KEY:
        logger.info("✓ OpenWeather API密钥已配置 - 天气查询功能可用")
    else:
        logger.info("✗ OpenWeather API密钥未配置 - 将使用模拟天气数据")

def main():
    """主函数"""
    # 设置环境
    setup_environment()
    
    # 导入应用
    from main import app
    
    # 获取配置
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    reload = os.getenv("RELOAD", "false").lower() == "true"
    
    logger = logging.getLogger(__name__)
    logger.info(f"启动LangChain智能体应用")
    logger.info(f"地址: http://{host}:{port}")
    logger.info(f"重载模式: {reload}")
    
    # 启动应用
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=reload,
        log_level=config.LOG_LEVEL.lower()
    )

if __name__ == "__main__":
    main()
