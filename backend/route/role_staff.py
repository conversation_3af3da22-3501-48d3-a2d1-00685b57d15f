from fastapi import APIRouter, UploadFile, File, HTTPException
from pydantic import BaseModel
from typing import List
import csv
from fastapi.responses import StreamingResponse
from io import StringIO
from api.experience import add_experience, ExperienceRecord
from api.role_staff import Role, Staff, roles, staffs
from datetime import datetime

router = APIRouter()

@router.get("/roles")
def get_roles(page: int = 1, size: int = 50):
    start = (page - 1) * size
    end = start + size
    total = len(roles)
    return {"total": total, "page": page, "size": size, "roles": [r.dict() for r in roles[start:end]]}

@router.post("/roles")
def add_role(role: Role):
    roles.append(role)
    return {"success": True}

@router.put("/roles/{code}")
def update_role(code: str, role: Role):
    for idx, r in enumerate(roles):
        if r.code == code:
            roles[idx] = role
            return {"success": True}
    raise HTTPException(status_code=404, detail="岗位未找到")

@router.delete("/roles/{code}")
def delete_role(code: str):
    global roles
    roles = [r for r in roles if r.code != code]
    return {"success": True}

@router.post("/roles/import")
def import_roles(file: UploadFile = File(...)):
    content = file.file.read().decode("utf-8")
    reader = csv.DictReader(content.splitlines())
    imported = []
    for row in reader:
        role = Role(**row)
        roles.append(role)
        imported.append(role.dict())
    return {"imported": imported}

@router.get("/roles/export")
def export_roles():
    output = StringIO()
    writer = csv.DictWriter(output, fieldnames=["code", "name", "description"])
    writer.writeheader()
    for r in roles:
        writer.writerow(r.dict())
    output.seek(0)
    return StreamingResponse(output, media_type="text/csv", headers={"Content-Disposition": "attachment; filename=roles.csv"})

@router.get("/staffs")
def get_staffs(page: int = 1, size: int = 50):
    start = (page - 1) * size
    end = start + size
    total = len(staffs)
    return {"total": total, "page": page, "size": size, "staffs": [s.dict() for s in staffs[start:end]]}

@router.post("/staffs")
def add_staff(staff: Staff, user_id: str = "system"):
    staffs.append(staff)
    # 记录经验
    add_experience(ExperienceRecord(
        user_id=user_id,
        type="staff",
        original={},
        modified=staff.dict(),
        timestamp=datetime.now().isoformat(),
        remark="新增员工"
    ))
    return {"success": True}

@router.put("/staffs/{job_no}")
def update_staff(job_no: str, staff: Staff, user_id: str = "system"):
    for idx, s in enumerate(staffs):
        if s.job_no == job_no:
            old = staffs[idx]
            staffs[idx] = staff
            # 记录经验
            add_experience(ExperienceRecord(
                user_id=user_id,
                type="staff",
                original=old.dict(),
                modified=staff.dict(),
                timestamp=datetime.now().isoformat(),
                remark="修改员工"
            ))
            return {"success": True}
    raise HTTPException(status_code=404, detail="人员未找到")

@router.delete("/staffs/{job_no}")
def delete_staff(job_no: str, user_id: str = "system"):
    global staffs
    old = next((s for s in staffs if s.job_no == job_no), None)
    staffs = [s for s in staffs if s.job_no != job_no]
    # 记录经验
    if old:
        add_experience(ExperienceRecord(
            user_id=user_id,
            type="staff",
            original=old.dict(),
            modified={},
            timestamp=datetime.now().isoformat(),
            remark="删除员工"
        ))
    return {"success": True}

@router.post("/staffs/import")
def import_staffs(file: UploadFile = File(...)):
    content = file.file.read().decode("utf-8")
    reader = csv.DictReader(content.splitlines())
    imported = []
    for row in reader:
        staff = Staff(**row)
        staffs.append(staff)
        imported.append(staff.dict())
    return {"imported": imported}

@router.get("/staffs/export")
def export_staffs():
    output = StringIO()
    writer = csv.DictWriter(output, fieldnames=["job_no", "name", "role_code", "phone", "status", "remark"])
    writer.writeheader()
    for s in staffs:
        writer.writerow(s.dict())
    output.seek(0)
    return StreamingResponse(output, media_type="text/csv", headers={"Content-Disposition": "attachment; filename=staffs.csv"}) 