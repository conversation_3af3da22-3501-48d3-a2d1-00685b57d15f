from fastapi import APIRouter, UploadFile, File, HTTPException, Request, Header
from fastapi.responses import StreamingResponse
from typing import Optional, AsyncGenerator
import os
import uuid
import tempfile
import logging
from core.ocr_utils import get_ocr_processor
from core.pdf_utils import get_pdf_parser
from core.ai_client import get_ai_client

router = APIRouter()
logger = logging.getLogger(__name__)

# 上传文件保存目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
UPLOAD_DIR = os.path.abspath(os.path.join(BASE_DIR, "../uploaded_files"))
os.makedirs(UPLOAD_DIR, exist_ok=True)

@router.post("/ai/test")
async def test_ai_connection(
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None)
):
    try:
        ai_client = get_ai_client(x_api_key, x_base_url, x_model)
        if not ai_client.is_configured():
            raise Exception("AI服务器未配置")
        result = await ai_client.analyze_receipt_content("你好，这是测试", None)
        if result.get("success"):
            return {"success": True, "message": "连接测试成功"}
        else:
            raise Exception(result.get("error", "AI测试失败"))
    except Exception as e:
        logger.error(f"测试AI连接时出错: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/agent/message")
async def agent_message(
    request: Request,
    content: Optional[str] = None,
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None)
):
    try:
        ai_client = get_ai_client(x_api_key, x_base_url, x_model)
        if not ai_client.is_configured():
            raise Exception("AI服务器未配置")
        if not content:
            body = await request.json()
            content = body.get("content", "")
        if not content:
            raise Exception("消息内容不能为空")
        async def token_stream() -> AsyncGenerator[str, None]:
            queue = []
            def stream_callback(token):
                queue.append(token)
            await ai_client.analyze_receipt_content(content, None, stream_callback)
            while queue:
                yield queue.pop(0)
        return StreamingResponse(token_stream(), media_type="text/plain")
    except Exception as e:
        logger.error(f"处理文本消息时出错: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/agent/upload")
async def agent_upload(
    file: UploadFile = File(...),
    x_api_key: Optional[str] = Header(None),
    x_base_url: Optional[str] = Header(None),
    x_model: Optional[str] = Header(None)
):
    try:
        ai_client = get_ai_client(x_api_key, x_base_url, x_model)
        if not ai_client.is_configured():
            raise Exception("AI服务器未配置")
        file_content = await file.read()
        filename = (file.filename or "").lower()
        file_ext = os.path.splitext(filename)[1]
        unique_name = f"{uuid.uuid4()}{file_ext}"
        save_path = os.path.join(UPLOAD_DIR, unique_name)
        with open(save_path, "wb") as f:
            f.write(file_content)
        if filename.endswith((".png", ".jpg", ".jpeg", ".bmp", ".tiff", ".tif", ".webp")):
            ocr_processor = get_ocr_processor()
            ocr_result = ocr_processor.extract_text(save_path)
            if not ocr_result.get("success"):
                raise Exception(ocr_result.get("error", "图片识别失败"))
            file_text = ocr_result["text"]
        elif filename.endswith(".pdf"):
            pdf_parser = get_pdf_parser()
            file_text = pdf_parser.extract_text(save_path)
        else:
            raise Exception("不支持的文件类型，请上传图片(PNG/JPG/BMP/TIFF/WEBP)或PDF文件")
        prompt = f"请根据以下文档内容生成会计凭证：\n{file_text}"
        async def token_stream() -> AsyncGenerator[str, None]:
            queue = []
            def stream_callback(token):
                queue.append(token)
            await ai_client.analyze_receipt_content(prompt, None, stream_callback)
            while queue:
                yield queue.pop(0)
        headers = {
            "X-File-Name": unique_name,
            "X-File-URL": f"/files/{unique_name}"
        }
        return StreamingResponse(token_stream(), media_type="text/plain", headers=headers)
    except Exception as e:
        logger.error(f"处理文件上传时出错: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e)) 