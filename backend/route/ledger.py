from fastapi import APIRouter
from pydantic import BaseModel
from typing import Optional, List
from fastapi.responses import StreamingResponse
from io import StringIO
import csv

from api.ledger import ledger_entries

router = APIRouter()

@router.get("/ledger")
def get_ledger(account_code: Optional[str] = None, detail: bool = False):
    if detail and account_code:
        entries = [e for e in ledger_entries if e.account_code == account_code]
    elif account_code:
        entries = [e for e in ledger_entries if e.account_code == account_code]
    else:
        entries = ledger_entries
    return {"ledger": [e.dict() for e in entries]}

@router.get("/ledger/export")
def export_ledger(account_code: Optional[str] = None, detail: bool = False):
    if detail and account_code:
        entries = [e for e in ledger_entries if e.account_code == account_code]
    elif account_code:
        entries = [e for e in ledger_entries if e.account_code == account_code]
    else:
        entries = ledger_entries
    output = StringIO()
    writer = csv.DictWriter(output, fieldnames=["date", "voucher_no", "summary", "account_code", "account_name", "debit", "credit", "balance", "direction"])
    writer.writeheader()
    for e in entries:
        writer.writerow(e.dict())
    output.seek(0)
    return StreamingResponse(output, media_type="text/csv", headers={"Content-Disposition": "attachment; filename=ledger.csv"}) 