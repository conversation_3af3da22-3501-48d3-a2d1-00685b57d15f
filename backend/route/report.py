from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from io import StringIO
import csv

router = APIRouter()

@router.get("/reports/balance-sheet")
def get_balance_sheet():
    data = [
        {"item": "流动资产", "amount": 100000},
        {"item": "非流动资产", "amount": 200000},
        {"item": "流动负债", "amount": 80000},
        {"item": "非流动负债", "amount": 50000},
        {"item": "所有者权益", "amount": 170000},
    ]
    return {"report": data}

@router.get("/reports/balance-sheet/export")
def export_balance_sheet():
    data = [
        {"item": "流动资产", "amount": 100000},
        {"item": "非流动资产", "amount": 200000},
        {"item": "流动负债", "amount": 80000},
        {"item": "非流动负债", "amount": 50000},
        {"item": "所有者权益", "amount": 170000},
    ]
    output = StringIO()
    writer = csv.DictWriter(output, fieldnames=["item", "amount"])
    writer.writeheader()
    for row in data:
        writer.writerow(row)
    output.seek(0)
    return StreamingResponse(output, media_type="text/csv", headers={"Content-Disposition": "attachment; filename=balance_sheet.csv"})

@router.get("/reports/income-statement")
def get_income_statement():
    data = [
        {"item": "营业收入", "amount": 150000},
        {"item": "营业成本", "amount": 90000},
        {"item": "税金及附加", "amount": 5000},
        {"item": "销售费用", "amount": 8000},
        {"item": "管理费用", "amount": 7000},
        {"item": "财务费用", "amount": 2000},
        {"item": "净利润", "amount": 38000},
    ]
    return {"report": data}

@router.get("/reports/income-statement/export")
def export_income_statement():
    data = [
        {"item": "营业收入", "amount": 150000},
        {"item": "营业成本", "amount": 90000},
        {"item": "税金及附加", "amount": 5000},
        {"item": "销售费用", "amount": 8000},
        {"item": "管理费用", "amount": 7000},
        {"item": "财务费用", "amount": 2000},
        {"item": "净利润", "amount": 38000},
    ]
    output = StringIO()
    writer = csv.DictWriter(output, fieldnames=["item", "amount"])
    writer.writeheader()
    for row in data:
        writer.writerow(row)
    output.seek(0)
    return StreamingResponse(output, media_type="text/csv", headers={"Content-Disposition": "attachment; filename=income_statement.csv"})

@router.get("/reports/cash-flow")
def get_cash_flow():
    data = [
        {"item": "经营活动现金流入", "amount": 120000},
        {"item": "经营活动现金流出", "amount": 70000},
        {"item": "投资活动现金流入", "amount": 30000},
        {"item": "投资活动现金流出", "amount": 20000},
        {"item": "筹资活动现金流入", "amount": 40000},
        {"item": "筹资活动现金流出", "amount": 15000},
        {"item": "现金及现金等价物净增加额", "amount": 67000},
    ]
    return {"report": data}

@router.get("/reports/cash-flow/export")
def export_cash_flow():
    data = [
        {"item": "经营活动现金流入", "amount": 120000},
        {"item": "经营活动现金流出", "amount": 70000},
        {"item": "投资活动现金流入", "amount": 30000},
        {"item": "投资活动现金流出", "amount": 20000},
        {"item": "筹资活动现金流入", "amount": 40000},
        {"item": "筹资活动现金流出", "amount": 15000},
        {"item": "现金及现金等价物净增加额", "amount": 67000},
    ]
    output = StringIO()
    writer = csv.DictWriter(output, fieldnames=["item", "amount"])
    writer.writeheader()
    for row in data:
        writer.writerow(row)
    output.seek(0)
    return StreamingResponse(output, media_type="text/csv", headers={"Content-Disposition": "attachment; filename=cash_flow.csv"}) 