from fastapi import APIRouter, UploadFile, File, HTTPException
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import csv
from fastapi.responses import StreamingResponse
from io import StringIO
import os
import json
from api.experience import add_experience, ExperienceRecord
from api.subject import SubjectAccount, subject_accounts, subject_dict, build_subject_tree, get_parent_code, get_subject_level, check_subject_consistency, SUBJECT_TEMPLATES, Template<PERSON>ey
from datetime import datetime
from config import TEMPLATE_DIR

router = APIRouter()

@router.get("/subjects/tree")
def get_subject_tree():
    return {"tree": build_subject_tree(subject_accounts)}

@router.get("/subjects")
def get_subjects(page: int = 1, size: int = 50, keyword: Optional[str] = None):
    filtered = subject_accounts
    if keyword:
        filtered = [s for s in subject_accounts if keyword in s.科目编码 or keyword in s.科目名称]
    total = len(filtered)
    start = (page - 1) * size
    end = start + size
    return {"total": total, "page": page, "size": size, "subjects": [s.dict() for s in filtered[start:end]]}


@router.post("/subjects")
def add_subject(subject: SubjectAccount, user_id: str = "system"):
    # 自动补全父级编码和级次
    subject.父级编码 = get_parent_code(subject.科目编码)
    subject.级次 = get_subject_level(subject.科目编码)
    # 新增：编码与名称层级一致性校验
    if not check_subject_consistency(subject.科目编码, subject.科目名称):
        raise HTTPException(status_code=400, detail=f"科目编码与名称层级不一致：编码{subject.科目编码}，名称{subject.科目名称}。请保证编码长度与名称'-'分隔层级一致，或补全缺失的上级科目。")
    if any(s.科目编码 == subject.科目编码 for s in subject_accounts):
        raise HTTPException(status_code=400, detail="科目编码已存在")
    if subject.父级编码:
        parent = next((s for s in subject_accounts if s.科目编码 == subject.父级编码), None)
        if not parent:
            raise HTTPException(status_code=400, detail="父级科目不存在")
        parent.末级 = False
    subject_accounts.append(subject)
    subject_dict[subject.科目编码] = subject
    # 记录经验
    add_experience(ExperienceRecord(
        user_id=user_id,
        type="subject",
        original={},
        modified=subject.dict(),
        timestamp=datetime.now().isoformat(),
        remark="新增科目"
    ))
    return {"success": True}

@router.put("/subjects/{code}")
def update_subject(code: str, subject: SubjectAccount, user_id: str = "system"):
    for idx, s in enumerate(subject_accounts):
        if s.科目编码 == code:
            old = subject_accounts[idx]
            subject_accounts[idx] = subject
            subject_dict[code] = subject
            # 记录经验
            add_experience(ExperienceRecord(
                user_id=user_id,
                type="subject",
                original=old.dict(),
                modified=subject.dict(),
                timestamp=datetime.now().isoformat(),
                remark="修改科目"
            ))
            return {"success": True}
    raise HTTPException(status_code=404, detail="科目未找到")

@router.delete("/subjects/{code}")
def delete_subject(code: str, user_id: str = "system"):
    global subject_accounts
    # 检查是否有下级科目
    if any(s.父级编码 == code for s in subject_accounts):
        raise HTTPException(status_code=400, detail="存在下级科目，无法删除")
    old = next((s for s in subject_accounts if s.科目编码 == code), None)
    subject_accounts = [s for s in subject_accounts if s.科目编码 != code]
    subject_dict.pop(code, None)
    # 记录经验
    if old:
        add_experience(ExperienceRecord(
            user_id=user_id,
            type="subject",
            original=old.dict(),
            modified={},
            timestamp=datetime.now().isoformat(),
            remark="删除科目"
        ))
    return {"success": True}

@router.patch("/subjects/{code}/status")
def patch_subject_status(code: str, status: str):
    for s in subject_accounts:
        if s.科目编码 == code:
            s.状态 = status
            return {"success": True}
    raise HTTPException(status_code=404, detail="科目未找到")

@router.post("/subjects/import")
def import_subjects(file: UploadFile = File(...)):
    content = file.file.read().decode("utf-8")
    reader = csv.DictReader(content.splitlines())
    imported = []
    for row in reader:
        code = row["科目编码"]
        # 自动补全父级编码和级次
        parent_code = get_parent_code(code)
        level = get_subject_level(code)
        subject = SubjectAccount(
            科目编码=code,
            科目名称=row["科目名称"],
            级次=level,
            父级编码=parent_code,
            类别=row["类别"],
            方向=row["方向"],
            辅助核算=[x.strip() for x in row["辅助核算"].split(",") if x.strip()] if row.get("辅助核算") else [],
            末级=row.get("末级", "True") == "True",
            状态=row.get("状态", "启用"),
            备注=row.get("备注", "")
        )
        if any(s.科目编码 == subject.科目编码 for s in subject_accounts):
            continue  # 跳过已存在
        subject_accounts.append(subject)
        subject_dict[subject.科目编码] = subject
        imported.append(subject.dict())
    return {"imported": imported}

@router.get("/subjects/export")
def export_subjects():
    output = StringIO()
    writer = csv.DictWriter(output, fieldnames=["科目编码", "科目名称", "级次", "父级编码", "类别", "方向", "辅助核算", "末级", "状态", "备注"])
    writer.writeheader()
    for s in subject_accounts:
        row = s.dict()
        row["辅助核算"] = ",".join(row["辅助核算"]) if row["辅助核算"] else ""
        writer.writerow(row)
    output.seek(0)
    return StreamingResponse(output, media_type="text/csv", headers={"Content-Disposition": "attachment; filename=subjects.csv"})

@router.get("/subjects/templates")
def get_subject_templates():
    return [{"key": t["key"], "name": t["name"], "desc": t["desc"]} for t in SUBJECT_TEMPLATES]

@router.post("/subjects/use-template")
def use_subject_template(data: TemplateKey):
    key = data.key
    template = next((t for t in SUBJECT_TEMPLATES if t["key"] == key), None)
    if not template:
        raise HTTPException(status_code=404, detail="模板不存在")
    global subject_accounts, subject_dict
    subject_accounts = [SubjectAccount(**s) for s in template["subjects"]]
    subject_dict = {s.科目编码: s for s in subject_accounts}
    return {"success": True, "count": len(subject_accounts)} 