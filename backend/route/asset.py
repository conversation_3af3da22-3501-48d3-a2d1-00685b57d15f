import imp
from fastapi import APIRouter, UploadFile, File, HTTPException
from pydantic import BaseModel
from typing import List
import csv
from fastapi.responses import StreamingResponse
from io import StringIO
from api.experience import add_experience, ExperienceRecord
from api.asset import AssetItem, assets
from datetime import datetime

router = APIRouter()

@router.get("/assets")
def get_assets(page: int = 1, size: int = 50):
    start = (page - 1) * size
    end = start + size
    total = len(assets)
    return {"total": total, "page": page, "size": size, "assets": [a.dict() for a in assets[start:end]]}

@router.post("/assets")
def add_asset(asset: AssetItem, user_id: str = "system"):
    assets.append(asset)
    # 记录经验
    add_experience(ExperienceRecord(
        user_id=user_id,
        type="asset",
        original={},
        modified=asset.dict(),
        timestamp=datetime.now().isoformat(),
        remark="新增资产"
    ))
    return {"success": True}

@router.put("/assets/{code}")
def update_asset(code: str, asset: AssetItem, user_id: str = "system"):
    for idx, a in enumerate(assets):
        if a.code == code:
            old = assets[idx]
            assets[idx] = asset
            # 记录经验
            add_experience(ExperienceRecord(
                user_id=user_id,
                type="asset",
                original=old.dict(),
                modified=asset.dict(),
                timestamp=datetime.now().isoformat(),
                remark="修改资产"
            ))
            return {"success": True}
    raise HTTPException(status_code=404, detail="资产未找到")

@router.delete("/assets/{code}")
def delete_asset(code: str, user_id: str = "system"):
    global assets
    old = next((a for a in assets if a.code == code), None)
    assets = [a for a in assets if a.code != code]
    # 记录经验
    if old:
        add_experience(ExperienceRecord(
            user_id=user_id,
            type="asset",
            original=old.dict(),
            modified={},
            timestamp=datetime.now().isoformat(),
            remark="删除资产"
        ))
    return {"success": True}

@router.post("/assets/import")
def import_assets(file: UploadFile = File(...)):
    content = file.file.read().decode("utf-8")
    reader = csv.DictReader(content.splitlines())
    imported = []
    for row in reader:
        asset_data = {
            'code': row.get('code', ''),
            'name': row.get('name', ''),
            'category': row.get('category', ''),
            'original_value': float(row.get('original_value', 0)),
            'net_value': float(row.get('net_value', 0)),
            'purchase_date': row.get('purchase_date', ''),
            'useful_life': int(row.get('useful_life', 0)),
            'status': row.get('status', ''),
            'remark': row.get('remark', ''),
        }
        asset = AssetItem(**asset_data)
        assets.append(asset)
        imported.append(asset.dict())
    return {"imported": imported}

@router.get("/assets/export")
def export_assets():
    output = StringIO()
    writer = csv.DictWriter(output, fieldnames=["code", "name", "category", "original_value", "net_value", "purchase_date", "useful_life", "status", "remark"])
    writer.writeheader()
    for a in assets:
        writer.writerow(a.dict())
    output.seek(0)
    return StreamingResponse(output, media_type="text/csv", headers={"Content-Disposition": "attachment; filename=assets.csv"}) 