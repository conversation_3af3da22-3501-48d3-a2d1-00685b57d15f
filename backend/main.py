from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os
import logging
import uvicorn
from route.subject import router as subject_router
from route.asset import router as asset_router
from route.role_staff import router as role_staff_router
from route.agent import router as agent_router
from route.voucher import router as voucher_router
from route.ledger import router as ledger_router
from route.report import router as report_router

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 上传文件保存目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
UPLOAD_DIR = os.path.join(BASE_DIR, "uploaded_files")
os.makedirs(UPLOAD_DIR, exist_ok=True)

# 初始化FastAPI应用
app = FastAPI()
app.mount("/files", StaticFiles(directory=UPLOAD_DIR), name="uploaded_files")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 根路由
@app.get("/")
async def root():
    return {"message": "会计应用 - 智能体功能"}

# 注册各业务模块路由
app.include_router(subject_router)
app.include_router(asset_router)
app.include_router(role_staff_router)
app.include_router(agent_router)
app.include_router(voucher_router)
app.include_router(ledger_router)
app.include_router(report_router)

# 启动命令
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
