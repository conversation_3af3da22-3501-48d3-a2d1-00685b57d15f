"""
智能体测试用例
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, patch, AsyncMock
import tempfile
import os

from core.agent import (
    get_agent_executor, get_llm_manager, get_memory, 
    get_prompt_manager, get_chain_manager, get_tool_registry
)


class TestLLMManager:
    """LLM管理器测试"""
    
    def test_llm_manager_singleton(self):
        """测试LLM管理器单例模式"""
        manager1 = get_llm_manager()
        manager2 = get_llm_manager()
        assert manager1 is manager2
    
    def test_llm_configuration(self):
        """测试LLM配置"""
        manager = get_llm_manager()
        
        # 初始状态未配置
        assert not manager.is_configured()
        
        # 配置LLM
        manager.configure(
            api_key="test_key",
            base_url="https://api.test.com",
            model="test_model"
        )
        
        # 配置后应该可用
        assert manager.is_configured()
        assert manager.get_llm() is not None


class TestMemoryManager:
    """记忆管理器测试"""
    
    def test_memory_creation(self):
        """测试记忆创建"""
        memory = get_memory("test_session")
        assert memory.session_id == "test_session"
    
    def test_memory_operations(self):
        """测试记忆操作"""
        memory = get_memory("test_session")
        
        # 添加消息
        memory.add_user_message("Hello")
        memory.add_ai_message("Hi there!")
        
        # 检查消息
        messages = memory.get_recent_messages(10)
        assert len(messages) == 2
        assert messages[0].content == "Hello"
        assert messages[1].content == "Hi there!"
        
        # 清空记忆
        memory.clear()
        messages = memory.get_recent_messages(10)
        assert len(messages) == 0


class TestPromptManager:
    """提示管理器测试"""
    
    def test_prompt_manager_singleton(self):
        """测试提示管理器单例模式"""
        manager1 = get_prompt_manager()
        manager2 = get_prompt_manager()
        assert manager1 is manager2
    
    def test_default_templates(self):
        """测试默认模板"""
        manager = get_prompt_manager()
        templates = manager.list_templates()
        
        # 检查默认模板是否存在
        expected_templates = [
            "system_base", "voucher_generation", 
            "document_analysis", "conversation_summary", "error_handling"
        ]
        for template in expected_templates:
            assert template in templates
    
    def test_template_operations(self):
        """测试模板操作"""
        manager = get_prompt_manager()
        
        # 注册新模板
        manager.register_template(
            name="test_template",
            template="Hello {name}!",
            variables=["name"],
            description="Test template"
        )
        
        # 检查模板是否注册成功
        assert "test_template" in manager.list_templates()
        
        # 构建提示
        prompt = manager.build_prompt("test_template", name="World")
        assert "Hello World!" in prompt
        
        # 删除模板
        assert manager.delete_template("test_template")
        assert "test_template" not in manager.list_templates()


class TestToolRegistry:
    """工具注册表测试"""
    
    def test_tool_registry_singleton(self):
        """测试工具注册表单例模式"""
        registry1 = get_tool_registry()
        registry2 = get_tool_registry()
        assert registry1 is registry2
    
    def test_default_tools(self):
        """测试默认工具"""
        registry = get_tool_registry()
        tools = registry.list_tools()
        
        # 检查默认工具是否存在
        expected_tools = [
            "pdf_processor", "ocr_processor", 
            "database_query", "voucher_creator", "web_search"
        ]
        for tool in expected_tools:
            assert tool in tools
    
    @pytest.mark.asyncio
    async def test_tool_execution(self):
        """测试工具执行"""
        registry = get_tool_registry()
        
        # 测试数据库查询工具
        result = await registry.execute_tool(
            "database_query",
            table="subjects",
            filters={},
            limit=10
        )
        
        assert result.success
        assert "data" in result.data
        assert isinstance(result.data["data"], list)


class TestChainManager:
    """链式调用管理器测试"""
    
    def test_chain_manager_singleton(self):
        """测试链式调用管理器单例模式"""
        manager1 = get_chain_manager()
        manager2 = get_chain_manager()
        assert manager1 is manager2
    
    def test_default_chains(self):
        """测试默认链式调用"""
        manager = get_chain_manager()
        chains = manager.list_chains()
        
        # 检查默认链式调用是否存在
        expected_chains = [
            "voucher_generation", "document_analysis", "conversation_summary"
        ]
        for chain in expected_chains:
            assert chain in chains


class TestAgentExecutor:
    """智能体执行器测试"""
    
    def test_agent_executor_creation(self):
        """测试智能体执行器创建"""
        agent = get_agent_executor("test_session")
        assert agent.session_id == "test_session"
    
    def test_agent_executor_singleton_per_session(self):
        """测试每个会话的智能体执行器单例"""
        agent1 = get_agent_executor("session1")
        agent2 = get_agent_executor("session1")
        agent3 = get_agent_executor("session2")
        
        assert agent1 is agent2
        assert agent1 is not agent3
    
    @pytest.mark.asyncio
    async def test_tool_execution(self):
        """测试工具执行"""
        agent = get_agent_executor("test_session")
        
        # 测试数据库查询工具
        result = await agent.execute_tool(
            "database_query",
            table="subjects"
        )
        
        assert result["success"]
        assert "data" in result
    
    def test_conversation_history(self):
        """测试对话历史"""
        agent = get_agent_executor("test_session")
        
        # 清空历史
        agent.clear_history()
        
        # 添加消息
        agent.memory.add_user_message("Hello")
        agent.memory.add_ai_message("Hi!")
        
        # 获取历史
        history = agent.get_conversation_history()
        assert len(history) == 2
        assert history[0]["role"] == "user"
        assert history[0]["content"] == "Hello"
        assert history[1]["role"] == "assistant"
        assert history[1]["content"] == "Hi!"


class TestIntegration:
    """集成测试"""
    
    @pytest.mark.asyncio
    async def test_document_processing_flow(self):
        """测试文档处理流程"""
        agent = get_agent_executor("test_session")
        
        # 创建临时PDF文件
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp_file:
            # 这里应该创建一个真实的PDF文件，但为了测试简化
            tmp_file.write(b"fake pdf content")
            tmp_file_path = tmp_file.name
        
        try:
            # 测试PDF处理工具
            result = await agent.execute_tool(
                "pdf_processor",
                file_path=tmp_file_path
            )
            
            # 由于是假的PDF文件，预期会失败
            assert not result["success"]
            assert "error" in result
        finally:
            # 清理临时文件
            os.unlink(tmp_file_path)
    
    @pytest.mark.asyncio
    async def test_web_search_tool(self):
        """测试网络搜索工具"""
        agent = get_agent_executor("test_session")
        
        # 测试网络搜索
        result = await agent.execute_tool(
            "web_search",
            query="Python programming",
            max_results=3
        )
        
        assert result["success"]
        assert "data" in result
        assert "results" in result["data"]
        assert len(result["data"]["results"]) <= 3


# 运行测试的辅助函数
def run_tests():
    """运行所有测试"""
    pytest.main([__file__, "-v"])


if __name__ == "__main__":
    run_tests()
