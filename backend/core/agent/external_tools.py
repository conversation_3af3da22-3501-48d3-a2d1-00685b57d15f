"""
外部工具集成 - 网络搜索、API调用等外部工具
"""

import logging
from typing import Dict, Any, List, Optional, Type
from pydantic import BaseModel, Field
import aiohttp
import json
import os
import re
import asyncio
from datetime import datetime
from urllib.parse import urlparse, quote

from .tools import BaseAgentTool, register_tool

logger = logging.getLogger(__name__)


class EnhancedWebSearchTool(BaseAgentTool):
    """增强网络搜索工具 - 使用DuckDuckGo搜索引擎"""

    name: str = "enhanced_web_search"
    description: str = "搜索网络信息，获取最新的知识和数据"
    
    class InputSchema(BaseModel):
        query: str = Field(description="搜索查询")
        max_results: int = Field(default=5, description="最大结果数")
    
    args_schema: Type[BaseModel] = InputSchema
    
    async def execute(self, query: str, max_results: int = 5) -> Dict[str, Any]:
        """执行网络搜索"""
        try:
            # 使用DuckDuckGo搜索
            try:
                from duckduckgo_search import DDGS
                
                with DDGS() as ddgs:
                    results = []
                    for r in ddgs.text(query, max_results=max_results):
                        results.append({
                            "title": r.get("title", ""),
                            "url": r.get("href", ""),
                            "snippet": r.get("body", "")
                        })
                    
                    return {
                        "query": query,
                        "results": results,
                        "count": len(results),
                        "timestamp": datetime.now().isoformat()
                    }
            except ImportError:
                # 如果没有安装duckduckgo_search，使用备用方法
                logger.warning("duckduckgo_search未安装，使用备用搜索方法")
                
                # 使用Serper API (如果有API密钥)
                serper_api_key = os.environ.get("SERPER_API_KEY")
                if serper_api_key:
                    return await self._search_with_serper(query, max_results, serper_api_key)
                
                # 否则返回模拟结果
                return {
                    "query": query,
                    "results": [
                        {
                            "title": f"搜索结果 {i+1}",
                            "url": f"https://example.com/result{i+1}",
                            "snippet": f"关于 '{query}' 的搜索结果 {i+1}"
                        }
                        for i in range(min(max_results, 3))
                    ],
                    "count": min(max_results, 3),
                    "timestamp": datetime.now().isoformat()
                }
        except Exception as e:
            logger.error(f"网络搜索失败: {str(e)}")
            raise Exception(f"网络搜索失败: {str(e)}")
    
    async def _search_with_serper(self, query: str, max_results: int, api_key: str) -> Dict[str, Any]:
        """使用Serper API搜索"""
        try:
            headers = {
                "X-API-KEY": api_key,
                "Content-Type": "application/json"
            }
            data = {
                "q": query,
                "num": max_results
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    "https://google.serper.dev/search",
                    headers=headers,
                    json=data
                ) as response:
                    if response.status != 200:
                        raise Exception(f"API请求失败: {response.status}")
                    
                    result = await response.json()
                    
                    organic = result.get("organic", [])
                    results = []
                    
                    for item in organic[:max_results]:
                        results.append({
                            "title": item.get("title", ""),
                            "url": item.get("link", ""),
                            "snippet": item.get("snippet", "")
                        })
                    
                    return {
                        "query": query,
                        "results": results,
                        "count": len(results),
                        "timestamp": datetime.now().isoformat()
                    }
        except Exception as e:
            logger.error(f"Serper API搜索失败: {str(e)}")
            raise Exception(f"搜索API调用失败: {str(e)}")


class WebFetchTool(BaseAgentTool):
    """网页内容获取工具"""

    name: str = "web_fetch"
    description: str = "获取网页内容，提取文本信息"
    
    class InputSchema(BaseModel):
        url: str = Field(description="网页URL")
    
    args_schema: Type[BaseModel] = InputSchema
    
    async def execute(self, url: str) -> Dict[str, Any]:
        """获取网页内容"""
        try:
            # 验证URL
            parsed_url = urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                raise ValueError("无效的URL")
            
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as response:
                    if response.status != 200:
                        raise Exception(f"获取网页失败: HTTP {response.status}")
                    
                    content_type = response.headers.get("Content-Type", "")
                    if "text/html" not in content_type:
                        raise Exception(f"不支持的内容类型: {content_type}")
                    
                    html = await response.text()
                    
                    # 使用BeautifulSoup提取文本
                    try:
                        from bs4 import BeautifulSoup
                        
                        soup = BeautifulSoup(html, "html.parser")
                        
                        # 移除脚本和样式
                        for script in soup(["script", "style"]):
                            script.extract()
                        
                        # 获取文本
                        text = soup.get_text()
                        
                        # 清理文本
                        lines = (line.strip() for line in text.splitlines())
                        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                        text = "\n".join(chunk for chunk in chunks if chunk)
                        
                        # 提取标题
                        title = soup.title.string if soup.title else ""
                        
                        return {
                            "url": url,
                            "title": title,
                            "text": text[:10000],  # 限制文本长度
                            "is_truncated": len(text) > 10000,
                            "timestamp": datetime.now().isoformat()
                        }
                    except ImportError:
                        # 如果没有安装BeautifulSoup，使用正则表达式提取
                        logger.warning("BeautifulSoup未安装，使用正则表达式提取文本")
                        
                        # 提取标题
                        title_match = re.search(r"<title>(.*?)</title>", html, re.IGNORECASE | re.DOTALL)
                        title = title_match.group(1) if title_match else ""
                        
                        # 移除HTML标签
                        text = re.sub(r"<[^>]+>", " ", html)
                        text = re.sub(r"\s+", " ", text).strip()
                        
                        return {
                            "url": url,
                            "title": title,
                            "text": text[:10000],
                            "is_truncated": len(text) > 10000,
                            "timestamp": datetime.now().isoformat()
                        }
        except Exception as e:
            logger.error(f"获取网页内容失败: {str(e)}")
            raise Exception(f"获取网页内容失败: {str(e)}")


class WeatherTool(BaseAgentTool):
    """天气查询工具"""

    name: str = "weather_query"
    description: str = "查询指定城市的天气信息"
    
    class InputSchema(BaseModel):
        city: str = Field(description="城市名称")
        country_code: str = Field(default="CN", description="国家代码，默认为中国(CN)")
    
    args_schema: Type[BaseModel] = InputSchema
    
    async def execute(self, city: str, country_code: str = "CN") -> Dict[str, Any]:
        """查询天气信息"""
        try:
            # 使用OpenWeatherMap API
            api_key = os.environ.get("OPENWEATHER_API_KEY")
            if not api_key:
                # 模拟天气数据
                return {
                    "city": city,
                    "country": country_code,
                    "temperature": 25,
                    "weather": "晴天",
                    "humidity": 60,
                    "wind_speed": 5.2,
                    "timestamp": datetime.now().isoformat(),
                    "note": "模拟数据，未配置API密钥"
                }
            
            # 编码城市名称
            encoded_city = quote(city)
            
            url = f"https://api.openweathermap.org/data/2.5/weather?q={encoded_city},{country_code}&appid={api_key}&units=metric&lang=zh_cn"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        error_data = await response.json()
                        error_msg = error_data.get("message", f"API请求失败: HTTP {response.status}")
                        raise Exception(error_msg)
                    
                    data = await response.json()
                    
                    return {
                        "city": data.get("name", city),
                        "country": country_code,
                        "temperature": data.get("main", {}).get("temp"),
                        "weather": data.get("weather", [{}])[0].get("description", ""),
                        "humidity": data.get("main", {}).get("humidity"),
                        "wind_speed": data.get("wind", {}).get("speed"),
                        "timestamp": datetime.now().isoformat()
                    }
        except Exception as e:
            logger.error(f"天气查询失败: {str(e)}")
            raise Exception(f"天气查询失败: {str(e)}")


# 获取外部工具列表
def get_external_tools():
    """获取所有外部工具"""
    return [
        EnhancedWebSearchTool(),
        WebFetchTool(),
        WeatherTool()
    ]
