"""
记忆系统 - 管理对话历史和长期记忆
"""

import logging
from typing import List, Dict, Any, Optional
from langchain_core.memory import BaseMemory
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain_core.chat_history import BaseChatMessageHistory
from pydantic import Field
import json
import os
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class InMemoryChatHistory(BaseChatMessageHistory):
    """内存中的聊天历史"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.messages: List[BaseMessage] = []
    
    def add_message(self, message: BaseMessage) -> None:
        """添加消息"""
        self.messages.append(message)
    
    def clear(self) -> None:
        """清空历史"""
        self.messages = []
    
    def get_messages(self) -> List[BaseMessage]:
        """获取所有消息"""
        return self.messages


class ConversationMemory:
    """对话记忆管理器"""

    def __init__(self, session_id: str, max_token_limit: int = 4000):
        self.session_id = session_id
        self.max_token_limit = max_token_limit
        self.chat_history = InMemoryChatHistory(session_id)
        self.context_data = {}  # 存储上下文数据
    
    @property
    def memory_variables(self) -> List[str]:
        """返回记忆变量名称"""
        return ["history", "context"]
    
    def load_memory_variables(self, inputs: Dict[str, Any]) -> Dict[str, Any]:
        """加载记忆变量"""
        # 获取聊天历史
        messages = self.chat_history.get_messages()
        
        # 格式化历史消息
        history_text = ""
        for msg in messages[-10:]:  # 只保留最近10条消息
            if isinstance(msg, HumanMessage):
                history_text += f"Human: {msg.content}\n"
            elif isinstance(msg, AIMessage):
                history_text += f"Assistant: {msg.content}\n"
        
        return {
            "history": history_text,
            "context": self.context_data
        }
    
    def save_context(self, inputs: Dict[str, Any], outputs: Dict[str, str]) -> None:
        """保存上下文"""
        # 保存用户输入
        if "input" in inputs:
            self.chat_history.add_message(HumanMessage(content=inputs["input"]))
        
        # 保存AI输出
        if "output" in outputs:
            self.chat_history.add_message(AIMessage(content=outputs["output"]))
    
    def clear(self) -> None:
        """清空记忆"""
        self.chat_history.clear()
        self.context_data = {}
    
    def add_context(self, key: str, value: Any) -> None:
        """添加上下文数据"""
        self.context_data[key] = value
    
    def get_context(self, key: str) -> Any:
        """获取上下文数据"""
        return self.context_data.get(key)
    
    def get_recent_messages(self, limit: int = 5) -> List[BaseMessage]:
        """获取最近的消息"""
        return self.chat_history.get_messages()[-limit:]
    
    def add_user_message(self, content: str) -> None:
        """添加用户消息"""
        self.chat_history.add_message(HumanMessage(content=content))
    
    def add_ai_message(self, content: str) -> None:
        """添加AI消息"""
        self.chat_history.add_message(AIMessage(content=content))
    
    def get_conversation_summary(self) -> str:
        """获取对话摘要"""
        messages = self.chat_history.get_messages()
        if not messages:
            return "暂无对话历史"
        
        # 简单的摘要逻辑
        total_messages = len(messages)
        user_messages = len([m for m in messages if isinstance(m, HumanMessage)])
        ai_messages = len([m for m in messages if isinstance(m, AIMessage)])
        
        return f"对话轮次: {total_messages}, 用户消息: {user_messages}, AI回复: {ai_messages}"


class MemoryManager:
    """记忆管理器"""
    
    def __init__(self):
        self._memories: Dict[str, ConversationMemory] = {}
        self._cleanup_interval = timedelta(hours=24)  # 24小时清理一次
        self._last_cleanup = datetime.now()
    
    def get_memory(self, session_id: str) -> ConversationMemory:
        """获取或创建会话记忆"""
        if session_id not in self._memories:
            self._memories[session_id] = ConversationMemory(session_id)
        
        # 定期清理过期记忆
        self._cleanup_expired_memories()
        
        return self._memories[session_id]
    
    def clear_memory(self, session_id: str) -> None:
        """清空指定会话的记忆"""
        if session_id in self._memories:
            self._memories[session_id].clear()
    
    def delete_memory(self, session_id: str) -> None:
        """删除指定会话的记忆"""
        if session_id in self._memories:
            del self._memories[session_id]
    
    def _cleanup_expired_memories(self) -> None:
        """清理过期的记忆"""
        now = datetime.now()
        if now - self._last_cleanup < self._cleanup_interval:
            return
        
        # 这里可以实现更复杂的清理逻辑
        # 目前只是更新清理时间
        self._last_cleanup = now
        logger.info("执行记忆清理检查")
    
    def get_all_sessions(self) -> List[str]:
        """获取所有会话ID"""
        return list(self._memories.keys())
    
    def get_session_count(self) -> int:
        """获取会话数量"""
        return len(self._memories)


# 全局记忆管理器实例
_memory_manager = None

def get_memory_manager() -> MemoryManager:
    """获取全局记忆管理器实例"""
    global _memory_manager
    if _memory_manager is None:
        _memory_manager = MemoryManager()
    return _memory_manager

def get_memory(session_id: str) -> ConversationMemory:
    """获取指定会话的记忆"""
    return get_memory_manager().get_memory(session_id)
