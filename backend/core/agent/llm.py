"""
LLM管理器 - 统一管理不同的语言模型
"""

import logging
from typing import Optional, Dict, Any, AsyncGenerator
from langchain_core.language_models.llms import BaseLLM
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from pydantic import Field
import aiohttp
import json

logger = logging.getLogger(__name__)


class CustomChatModel(BaseChatModel):
    """自定义聊天模型，兼容OpenAI API格式"""

    api_key: str = Field(...)
    base_url: str = Field(...)
    model: str = Field(...)
    temperature: float = Field(default=0.2)
    top_p: float = Field(default=0.8)

    def __init__(self, api_key: str, base_url: str, model: str, **kwargs):
        super().__init__(
            api_key=api_key,
            base_url=base_url,
            model=model,
            temperature=kwargs.get('temperature', 0.2),
            top_p=kwargs.get('top_p', 0.8),
            **kwargs
        )
    
    @property
    def _llm_type(self) -> str:
        return "custom_chat"
    
    def _generate(self, messages, stop=None, run_manager=None, **kwargs):
        # 同步版本，暂时抛出异常，强制使用异步版本
        raise NotImplementedError("请使用异步版本 _agenerate")
    
    async def _agenerate(self, messages, stop=None, run_manager=None, **kwargs):
        """异步生成响应"""
        try:
            # 转换消息格式
            formatted_messages = []
            for msg in messages:
                if hasattr(msg, 'content'):
                    if msg.__class__.__name__ == 'SystemMessage':
                        formatted_messages.append({"role": "system", "content": msg.content})
                    elif msg.__class__.__name__ == 'HumanMessage':
                        formatted_messages.append({"role": "user", "content": msg.content})
                    elif msg.__class__.__name__ == 'AIMessage':
                        formatted_messages.append({"role": "assistant", "content": msg.content})
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            data = {
                "model": self.model,
                "messages": formatted_messages,
                "temperature": self.temperature,
                "top_p": self.top_p,
                "stream": False
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=data
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"API请求失败: {response.status}, {error_text}")
                    
                    result = await response.json()
                    content = result.get("choices", [{}])[0].get("message", {}).get("content", "")
                    
                    from langchain_core.outputs import ChatGeneration, ChatResult
                    message = AIMessage(content=content)
                    generation = ChatGeneration(message=message)
                    return ChatResult(generations=[generation])
                    
        except Exception as e:
            logger.error(f"LLM生成失败: {str(e)}")
            raise
    
    async def astream(self, messages, stop=None, run_manager=None, **kwargs) -> AsyncGenerator[str, None]:
        """流式生成响应"""
        try:
            # 转换消息格式
            formatted_messages = []
            for msg in messages:
                if hasattr(msg, 'content'):
                    if msg.__class__.__name__ == 'SystemMessage':
                        formatted_messages.append({"role": "system", "content": msg.content})
                    elif msg.__class__.__name__ == 'HumanMessage':
                        formatted_messages.append({"role": "user", "content": msg.content})
                    elif msg.__class__.__name__ == 'AIMessage':
                        formatted_messages.append({"role": "assistant", "content": msg.content})
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            data = {
                "model": self.model,
                "messages": formatted_messages,
                "temperature": self.temperature,
                "top_p": self.top_p,
                "stream": True
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=data
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"API请求失败: {response.status}, {error_text}")
                    
                    buffer = ""
                    async for chunk in response.content.iter_chunked(1024):
                        buffer += chunk.decode('utf-8')
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            line = line.strip()
                            if line.startswith('data: '):
                                payload = line[6:]
                                if payload == '[DONE]':
                                    return
                                try:
                                    data = json.loads(payload)
                                    delta = data.get('choices', [{}])[0].get('delta', {})
                                    content = delta.get('content', '')
                                    if content:
                                        yield content
                                except json.JSONDecodeError:
                                    continue
                                
        except Exception as e:
            logger.error(f"流式生成失败: {str(e)}")
            raise


class LLMManager:
    """LLM管理器"""
    
    def __init__(self):
        self._llm = None
        self._config = {}
    
    def configure(self, api_key: str, base_url: str, model: str, **kwargs):
        """配置LLM"""
        self._config = {
            'api_key': api_key,
            'base_url': base_url,
            'model': model,
            **kwargs
        }
        
        # 创建自定义聊天模型
        self._llm = CustomChatModel(
            api_key=api_key,
            base_url=base_url,
            model=model,
            **kwargs
        )
        
        logger.info(f"LLM配置成功: {model}")
    
    def get_llm(self) -> Optional[BaseChatModel]:
        """获取LLM实例"""
        return self._llm
    
    def is_configured(self) -> bool:
        """检查是否已配置"""
        return self._llm is not None
    
    async def generate(self, messages, **kwargs):
        """生成响应"""
        if not self.is_configured():
            raise Exception("LLM未配置")
        return await self._llm._agenerate(messages, **kwargs)
    
    async def stream(self, messages, **kwargs) -> AsyncGenerator[str, None]:
        """流式生成响应"""
        if not self.is_configured():
            raise Exception("LLM未配置")
        async for token in self._llm.astream(messages, **kwargs):
            yield token


# 全局LLM管理器实例
_llm_manager = None

def get_llm_manager() -> LLMManager:
    """获取全局LLM管理器实例"""
    global _llm_manager
    if _llm_manager is None:
        _llm_manager = LLMManager()
    return _llm_manager
