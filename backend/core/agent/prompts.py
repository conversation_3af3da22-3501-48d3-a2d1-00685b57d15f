"""
提示管理系统 - 管理和组装各种提示模板
"""

import logging
from typing import Dict, Any, List, Optional
from langchain_core.prompts import ChatPromptTemplate, SystemMessagePromptTemplate, HumanMessagePromptTemplate
from langchain_core.prompts.base import BasePromptTemplate
from datetime import datetime
import json

logger = logging.getLogger(__name__)


class PromptTemplate:
    """提示模板类"""
    
    def __init__(self, name: str, template: str, variables: List[str], description: str = ""):
        self.name = name
        self.template = template
        self.variables = variables
        self.description = description
        self.created_at = datetime.now()
    
    def format(self, **kwargs) -> str:
        """格式化模板"""
        try:
            return self.template.format(**kwargs)
        except KeyError as e:
            logger.error(f"模板变量缺失: {e}")
            raise ValueError(f"模板 {self.name} 缺少必需变量: {e}")
    
    def validate_variables(self, variables: Dict[str, Any]) -> bool:
        """验证变量是否完整"""
        missing = set(self.variables) - set(variables.keys())
        if missing:
            logger.warning(f"模板 {self.name} 缺少变量: {missing}")
            return False
        return True


class PromptManager:
    """提示管理器"""
    
    def __init__(self):
        self._templates: Dict[str, PromptTemplate] = {}
        self._initialize_default_templates()
    
    def _initialize_default_templates(self):
        """初始化默认模板"""
        
        # 系统提示模板
        self.register_template(
            name="system_base",
            template="""你是一个专业的企业会计助手，具备以下能力：
1. 根据用户输入生成准确的会计凭证
2. 创建和管理科目、资产、员工信息
3. 提供专业的会计建议和解释
4. 处理各种财务文档和票据

当前系统信息：
- 日期: {current_date}
- 用户ID: {user_id}

请始终保持专业、准确、友好的态度。""",
            variables=["current_date", "user_id"],
            description="基础系统提示"
        )
        
        # 会计凭证生成模板
        self.register_template(
            name="voucher_generation",
            template="""基于以下信息生成会计凭证：

用户输入: {user_input}

可用科目表:
{subjects}

可用资产表:
{assets}

可用员工表:
{staff}

历史经验:
{experience}

请按照以下格式返回JSON：
{{
    "analysis": "对用户输入的分析",
    "cards": [
        {{
            "type": "subject",
            "op": "create|skip|update",
            "data": {{
                "科目编码": "1001",
                "科目名称": "库存现金",
                "类别": "资产",
                "方向": "借",
                "备注": ""
            }}
        }},
        {{
            "type": "voucher",
            "op": "create",
            "data": {{
                "凭证号": "记-001",
                "日期": "{current_date}",
                "摘要": "收到现金",
                "借方科目": "1001",
                "借方金额": 1000.00,
                "贷方科目": "6001",
                "贷方金额": 1000.00,
                "备注": ""
            }}
        }}
    ]
}}

重要规则：
1. 严格按照顺序生成：科目 -> 资产 -> 员工 -> 凭证
2. 确保借贷平衡
3. 日期格式为YYYY-MM-DD
4. 金额保留两位小数""",
            variables=["user_input", "subjects", "assets", "staff", "experience", "current_date"],
            description="会计凭证生成模板"
        )
        
        # 文档分析模板
        self.register_template(
            name="document_analysis",
            template="""请分析以下文档内容并生成相应的会计处理：

文档类型: {document_type}
文档内容:
{document_content}

请提取以下信息：
1. 交易日期
2. 交易金额
3. 交易对象
4. 交易性质
5. 相关科目建议

然后生成相应的会计凭证。""",
            variables=["document_type", "document_content"],
            description="文档分析模板"
        )
        
        # 对话总结模板
        self.register_template(
            name="conversation_summary",
            template="""请总结以下对话内容：

对话历史:
{conversation_history}

请提供：
1. 主要讨论的会计问题
2. 生成的凭证数量
3. 涉及的科目类别
4. 需要后续处理的事项""",
            variables=["conversation_history"],
            description="对话总结模板"
        )
        
        # 错误处理模板
        self.register_template(
            name="error_handling",
            template="""处理过程中出现错误：

错误类型: {error_type}
错误信息: {error_message}
用户输入: {user_input}

请提供：
1. 错误原因分析
2. 解决建议
3. 替代方案（如果有）

请用友好的语言向用户解释。""",
            variables=["error_type", "error_message", "user_input"],
            description="错误处理模板"
        )
    
    def register_template(self, name: str, template: str, variables: List[str], description: str = "") -> None:
        """注册新模板"""
        self._templates[name] = PromptTemplate(name, template, variables, description)
        logger.info(f"注册提示模板: {name}")
    
    def get_template(self, name: str) -> Optional[PromptTemplate]:
        """获取模板"""
        return self._templates.get(name)
    
    def list_templates(self) -> List[str]:
        """列出所有模板名称"""
        return list(self._templates.keys())
    
    def delete_template(self, name: str) -> bool:
        """删除模板"""
        if name in self._templates:
            del self._templates[name]
            logger.info(f"删除提示模板: {name}")
            return True
        return False
    
    def build_prompt(self, template_name: str, **kwargs) -> str:
        """构建提示"""
        template = self.get_template(template_name)
        if not template:
            raise ValueError(f"模板不存在: {template_name}")
        
        # 添加默认变量
        default_vars = {
            "current_date": datetime.now().strftime("%Y-%m-%d"),
            "user_id": kwargs.get("user_id", "anonymous")
        }
        
        # 合并变量
        variables = {**default_vars, **kwargs}
        
        # 验证变量
        if not template.validate_variables(variables):
            missing = set(template.variables) - set(variables.keys())
            raise ValueError(f"缺少必需变量: {missing}")
        
        return template.format(**variables)
    
    def build_chat_prompt(self, system_template: str, user_template: str, **kwargs) -> ChatPromptTemplate:
        """构建聊天提示"""
        system_prompt = self.build_prompt(system_template, **kwargs)
        user_prompt = self.build_prompt(user_template, **kwargs)
        
        return ChatPromptTemplate.from_messages([
            SystemMessagePromptTemplate.from_template(system_prompt),
            HumanMessagePromptTemplate.from_template(user_prompt)
        ])
    
    def export_templates(self) -> Dict[str, Any]:
        """导出所有模板"""
        return {
            name: {
                "template": template.template,
                "variables": template.variables,
                "description": template.description,
                "created_at": template.created_at.isoformat()
            }
            for name, template in self._templates.items()
        }
    
    def import_templates(self, templates_data: Dict[str, Any]) -> None:
        """导入模板"""
        for name, data in templates_data.items():
            self.register_template(
                name=name,
                template=data["template"],
                variables=data["variables"],
                description=data.get("description", "")
            )


# 全局提示管理器实例
_prompt_manager = None

def get_prompt_manager() -> PromptManager:
    """获取全局提示管理器实例"""
    global _prompt_manager
    if _prompt_manager is None:
        _prompt_manager = PromptManager()
    return _prompt_manager
