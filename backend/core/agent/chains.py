"""
链式调用引擎 - 管理和执行各种链式调用
"""

import logging
from typing import Dict, Any, List, Optional, Callable, Type, Union
from langchain_core.chains import Chain
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser, JsonOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableLambda
import json
import asyncio
from datetime import datetime

logger = logging.getLogger(__name__)


class ChainResult:
    """链式调用结果"""
    
    def __init__(self, success: bool, data: Any = None, error: Optional[str] = None):
        self.success = success
        self.data = data
        self.error = error
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "success": self.success,
            "data": self.data,
            "error": self.error,
            "timestamp": self.timestamp.isoformat()
        }


class BaseChain:
    """基础链式调用"""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.chain = None
    
    def build(self, llm: BaseChatModel, **kwargs) -> None:
        """构建链式调用"""
        raise NotImplementedError("子类必须实现 build 方法")
    
    async def run(self, inputs: Dict[str, Any]) -> ChainResult:
        """执行链式调用"""
        if not self.chain:
            return ChainResult(
                success=False,
                error="链式调用未构建"
            )
        
        try:
            result = await self.chain.ainvoke(inputs)
            return ChainResult(
                success=True,
                data=result
            )
        except Exception as e:
            logger.error(f"链式调用 {self.name} 执行失败: {str(e)}")
            return ChainResult(
                success=False,
                error=str(e)
            )
    
    async def stream(self, inputs: Dict[str, Any]):
        """流式执行链式调用"""
        if not self.chain:
            yield ChainResult(
                success=False,
                error="链式调用未构建"
            )
            return
        
        try:
            async for chunk in self.chain.astream(inputs):
                yield chunk
        except Exception as e:
            logger.error(f"链式调用 {self.name} 流式执行失败: {str(e)}")
            yield ChainResult(
                success=False,
                error=str(e)
            )


class VoucherGenerationChain(BaseChain):
    """凭证生成链"""
    
    def __init__(self):
        super().__init__(
            name="voucher_generation",
            description="生成会计凭证"
        )
    
    def build(self, llm: BaseChatModel, **kwargs) -> None:
        """构建链式调用"""
        from .prompts import get_prompt_manager
        
        prompt_manager = get_prompt_manager()
        system_prompt = prompt_manager.get_template("system_base")
        voucher_prompt = prompt_manager.get_template("voucher_generation")
        
        # 构建提示模板
        chat_prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt.template),
            ("human", voucher_prompt.template)
        ])
        
        # 构建解析器
        class VoucherOutputParser(JsonOutputParser):
            def parse(self, text):
                # 清理文本，提取JSON部分
                cleaned_text = text
                if "```json" in text:
                    cleaned_text = text.split("```json")[1].split("```")[0].strip()
                elif "```" in text:
                    cleaned_text = text.split("```")[1].split("```")[0].strip()
                
                try:
                    return json.loads(cleaned_text)
                except json.JSONDecodeError:
                    # 尝试修复常见的JSON错误
                    fixed_text = cleaned_text.replace("'", '"')
                    try:
                        return json.loads(fixed_text)
                    except:
                        logger.error(f"JSON解析失败: {cleaned_text}")
                        return {"error": "无法解析响应", "raw_text": text}
        
        # 构建链式调用
        self.chain = (
            {"user_input": lambda x: x["user_input"],
             "subjects": lambda x: json.dumps(x.get("subjects", []), ensure_ascii=False),
             "assets": lambda x: json.dumps(x.get("assets", []), ensure_ascii=False),
             "staff": lambda x: json.dumps(x.get("staff", []), ensure_ascii=False),
             "experience": lambda x: json.dumps(x.get("experience", []), ensure_ascii=False),
             "current_date": lambda x: x.get("current_date", datetime.now().strftime("%Y-%m-%d")),
             "user_id": lambda x: x.get("user_id", "anonymous")}
            | chat_prompt
            | llm
            | VoucherOutputParser()
        )


class DocumentAnalysisChain(BaseChain):
    """文档分析链"""
    
    def __init__(self):
        super().__init__(
            name="document_analysis",
            description="分析文档内容"
        )
    
    def build(self, llm: BaseChatModel, **kwargs) -> None:
        """构建链式调用"""
        from .prompts import get_prompt_manager
        
        prompt_manager = get_prompt_manager()
        system_prompt = prompt_manager.get_template("system_base")
        document_prompt = prompt_manager.get_template("document_analysis")
        
        # 构建提示模板
        chat_prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt.template),
            ("human", document_prompt.template)
        ])
        
        # 构建链式调用
        self.chain = (
            {"document_type": lambda x: x["document_type"],
             "document_content": lambda x: x["document_content"],
             "current_date": lambda x: x.get("current_date", datetime.now().strftime("%Y-%m-%d")),
             "user_id": lambda x: x.get("user_id", "anonymous")}
            | chat_prompt
            | llm
            | StrOutputParser()
        )


class ConversationSummaryChain(BaseChain):
    """对话总结链"""
    
    def __init__(self):
        super().__init__(
            name="conversation_summary",
            description="总结对话内容"
        )
    
    def build(self, llm: BaseChatModel, **kwargs) -> None:
        """构建链式调用"""
        from .prompts import get_prompt_manager
        
        prompt_manager = get_prompt_manager()
        system_prompt = prompt_manager.get_template("system_base")
        summary_prompt = prompt_manager.get_template("conversation_summary")
        
        # 构建提示模板
        chat_prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt.template),
            ("human", summary_prompt.template)
        ])
        
        # 构建链式调用
        self.chain = (
            {"conversation_history": lambda x: x["conversation_history"],
             "current_date": lambda x: x.get("current_date", datetime.now().strftime("%Y-%m-%d")),
             "user_id": lambda x: x.get("user_id", "anonymous")}
            | chat_prompt
            | llm
            | StrOutputParser()
        )


class ChainManager:
    """链式调用管理器"""
    
    def __init__(self):
        self._chains: Dict[str, BaseChain] = {}
        self._llm = None
    
    def set_llm(self, llm: BaseChatModel) -> None:
        """设置语言模型"""
        self._llm = llm
        # 重新构建所有链
        for chain in self._chains.values():
            chain.build(llm)
    
    def register_chain(self, chain: BaseChain) -> None:
        """注册链式调用"""
        self._chains[chain.name] = chain
        if self._llm:
            chain.build(self._llm)
        logger.info(f"注册链式调用: {chain.name}")
    
    def get_chain(self, name: str) -> Optional[BaseChain]:
        """获取链式调用"""
        return self._chains.get(name)
    
    def list_chains(self) -> List[str]:
        """列出所有链式调用名称"""
        return list(self._chains.keys())
    
    def remove_chain(self, name: str) -> bool:
        """移除链式调用"""
        if name in self._chains:
            del self._chains[name]
            logger.info(f"移除链式调用: {name}")
            return True
        return False
    
    async def run_chain(self, name: str, inputs: Dict[str, Any]) -> ChainResult:
        """执行链式调用"""
        chain = self.get_chain(name)
        if not chain:
            return ChainResult(
                success=False,
                error=f"链式调用不存在: {name}"
            )
        
        if not self._llm:
            return ChainResult(
                success=False,
                error="语言模型未设置"
            )
        
        return await chain.run(inputs)
    
    async def stream_chain(self, name: str, inputs: Dict[str, Any]):
        """流式执行链式调用"""
        chain = self.get_chain(name)
        if not chain:
            yield ChainResult(
                success=False,
                error=f"链式调用不存在: {name}"
            )
            return
        
        if not self._llm:
            yield ChainResult(
                success=False,
                error="语言模型未设置"
            )
            return
        
        async for chunk in chain.stream(inputs):
            yield chunk
    
    def initialize_default_chains(self) -> None:
        """初始化默认链式调用"""
        default_chains = [
            VoucherGenerationChain(),
            DocumentAnalysisChain(),
            ConversationSummaryChain()
        ]
        
        for chain in default_chains:
            self.register_chain(chain)


# 全局链式调用管理器实例
_chain_manager = None

def get_chain_manager() -> ChainManager:
    """获取全局链式调用管理器实例"""
    global _chain_manager
    if _chain_manager is None:
        _chain_manager = ChainManager()
        _chain_manager.initialize_default_chains()
    return _chain_manager
