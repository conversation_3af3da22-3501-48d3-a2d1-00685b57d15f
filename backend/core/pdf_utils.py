import os
import fitz  # PyMuPDF
from typing import Dict
import logging

logger = logging.getLogger(__name__)

class PDFParser:
    """PDF 文档解析器，基于 PyMuPDF"""
    def __init__(self):
        pass

    def extract_text(self, file_path: str) -> str:
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"PDF 文件不存在: {file_path}")
            doc = fitz.open(file_path)
            text_content = ""
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text_content += page.get_text()
                text_content += "\n\n"
            doc.close()
            return text_content.strip()
        except Exception as e:
            logger.error(f"PDF 文本提取失败: {str(e)}")
            raise Exception(f"PDF 文本提取失败: {str(e)}")

# 全局 PDF 解析器实例
_pdf_parser = None

def get_pdf_parser() -> PDFParser:
    global _pdf_parser
    if _pdf_parser is None:
        _pdf_parser = PDFParser()
    return _pdf_parser 