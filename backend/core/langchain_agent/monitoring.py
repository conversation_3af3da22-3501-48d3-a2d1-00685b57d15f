"""
性能监控和优化模块
"""

import logging
import time
import asyncio
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from collections import defaultdict, deque
import json
import threading
from functools import wraps

logger = logging.getLogger(__name__)


class PerformanceMetrics:
    """性能指标收集器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.metrics = defaultdict(lambda: deque(maxlen=max_history))
        self.counters = defaultdict(int)
        self.lock = threading.Lock()
    
    def record_execution_time(self, operation: str, execution_time: float):
        """记录执行时间"""
        with self.lock:
            self.metrics[f"{operation}_execution_time"].append({
                "timestamp": datetime.now(),
                "value": execution_time
            })
            self.counters[f"{operation}_count"] += 1
    
    def record_error(self, operation: str, error: str):
        """记录错误"""
        with self.lock:
            self.metrics[f"{operation}_errors"].append({
                "timestamp": datetime.now(),
                "error": error
            })
            self.counters[f"{operation}_error_count"] += 1
    
    def record_memory_usage(self, operation: str, memory_mb: float):
        """记录内存使用"""
        with self.lock:
            self.metrics[f"{operation}_memory"].append({
                "timestamp": datetime.now(),
                "value": memory_mb
            })
    
    def get_average_execution_time(self, operation: str, minutes: int = 60) -> Optional[float]:
        """获取平均执行时间"""
        with self.lock:
            key = f"{operation}_execution_time"
            if key not in self.metrics:
                return None
            
            cutoff_time = datetime.now() - timedelta(minutes=minutes)
            recent_metrics = [
                m for m in self.metrics[key] 
                if m["timestamp"] > cutoff_time
            ]
            
            if not recent_metrics:
                return None
            
            return sum(m["value"] for m in recent_metrics) / len(recent_metrics)
    
    def get_error_rate(self, operation: str, minutes: int = 60) -> float:
        """获取错误率"""
        with self.lock:
            error_key = f"{operation}_errors"
            total_key = f"{operation}_count"
            
            if error_key not in self.metrics:
                return 0.0
            
            cutoff_time = datetime.now() - timedelta(minutes=minutes)
            recent_errors = len([
                m for m in self.metrics[error_key] 
                if m["timestamp"] > cutoff_time
            ])
            
            # 估算总请求数（基于计数器和时间窗口）
            total_requests = max(recent_errors, self.counters.get(total_key, 0) // (1440 // minutes))
            
            if total_requests == 0:
                return 0.0
            
            return recent_errors / total_requests
    
    def get_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        with self.lock:
            summary = {}
            
            # 获取所有操作
            operations = set()
            for key in self.metrics.keys():
                if "_execution_time" in key:
                    operations.add(key.replace("_execution_time", ""))
            
            for operation in operations:
                avg_time = self.get_average_execution_time(operation)
                error_rate = self.get_error_rate(operation)
                
                summary[operation] = {
                    "average_execution_time": avg_time,
                    "error_rate": error_rate,
                    "total_requests": self.counters.get(f"{operation}_count", 0),
                    "total_errors": self.counters.get(f"{operation}_error_count", 0)
                }
            
            return summary


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = PerformanceMetrics()
        self.alerts = []
        self.alert_thresholds = {
            "execution_time": 10.0,  # 秒
            "error_rate": 0.1,       # 10%
            "memory_usage": 500.0    # MB
        }
    
    def monitor_execution(self, operation: str):
        """执行监控装饰器"""
        def decorator(func):
            if asyncio.iscoroutinefunction(func):
                @wraps(func)
                async def async_wrapper(*args, **kwargs):
                    start_time = time.time()
                    try:
                        result = await func(*args, **kwargs)
                        execution_time = time.time() - start_time
                        self.metrics.record_execution_time(operation, execution_time)
                        
                        # 检查性能阈值
                        self._check_performance_alerts(operation, execution_time)
                        
                        return result
                    except Exception as e:
                        execution_time = time.time() - start_time
                        self.metrics.record_execution_time(operation, execution_time)
                        self.metrics.record_error(operation, str(e))
                        raise
                return async_wrapper
            else:
                @wraps(func)
                def sync_wrapper(*args, **kwargs):
                    start_time = time.time()
                    try:
                        result = func(*args, **kwargs)
                        execution_time = time.time() - start_time
                        self.metrics.record_execution_time(operation, execution_time)
                        
                        # 检查性能阈值
                        self._check_performance_alerts(operation, execution_time)
                        
                        return result
                    except Exception as e:
                        execution_time = time.time() - start_time
                        self.metrics.record_execution_time(operation, execution_time)
                        self.metrics.record_error(operation, str(e))
                        raise
                return sync_wrapper
        return decorator
    
    def _check_performance_alerts(self, operation: str, execution_time: float):
        """检查性能警报"""
        if execution_time > self.alert_thresholds["execution_time"]:
            alert = {
                "type": "slow_execution",
                "operation": operation,
                "execution_time": execution_time,
                "threshold": self.alert_thresholds["execution_time"],
                "timestamp": datetime.now()
            }
            self.alerts.append(alert)
            logger.warning(f"性能警报: {operation} 执行时间过长 ({execution_time:.2f}s)")
        
        error_rate = self.metrics.get_error_rate(operation)
        if error_rate > self.alert_thresholds["error_rate"]:
            alert = {
                "type": "high_error_rate",
                "operation": operation,
                "error_rate": error_rate,
                "threshold": self.alert_thresholds["error_rate"],
                "timestamp": datetime.now()
            }
            self.alerts.append(alert)
            logger.warning(f"性能警报: {operation} 错误率过高 ({error_rate:.2%})")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        summary = self.metrics.get_summary()
        recent_alerts = [
            alert for alert in self.alerts[-10:]  # 最近10个警报
        ]
        
        return {
            "summary": summary,
            "alerts": recent_alerts,
            "timestamp": datetime.now().isoformat()
        }
    
    def clear_alerts(self):
        """清空警报"""
        self.alerts.clear()


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 3600):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self.cache = {}
        self.access_times = {}
        self.lock = threading.Lock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            if key not in self.cache:
                return None
            
            # 检查是否过期
            if time.time() - self.access_times[key] > self.ttl_seconds:
                del self.cache[key]
                del self.access_times[key]
                return None
            
            # 更新访问时间
            self.access_times[key] = time.time()
            return self.cache[key]
    
    def set(self, key: str, value: Any):
        """设置缓存值"""
        with self.lock:
            # 如果缓存已满，删除最旧的条目
            if len(self.cache) >= self.max_size:
                oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
                del self.cache[oldest_key]
                del self.access_times[oldest_key]
            
            self.cache[key] = value
            self.access_times[key] = time.time()
    
    def delete(self, key: str):
        """删除缓存值"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                del self.access_times[key]
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "ttl_seconds": self.ttl_seconds,
                "keys": list(self.cache.keys())
            }


# 全局监控实例
_performance_monitor = None
_cache_manager = None

def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器实例"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor

def get_cache_manager() -> CacheManager:
    """获取全局缓存管理器实例"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = CacheManager()
    return _cache_manager

# 便捷装饰器
def monitor_performance(operation: str):
    """性能监控装饰器"""
    return get_performance_monitor().monitor_execution(operation)
