"""
智能体执行器 - 协调各个组件，执行智能体任务
"""

import logging
from typing import Dict, Any, List, Optional, Callable, AsyncGenerator
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
import json
import asyncio
from datetime import datetime
import uuid

from .llm import get_llm_manager
from .memory import get_memory
from .tools import get_tools, get_tool_registry
from .prompts import get_prompt_manager
from .chains import get_chain_manager
from .monitoring import monitor_performance, get_cache_manager

logger = logging.getLogger(__name__)


class AgentExecutor:
    """智能体执行器"""
    
    def __init__(self, session_id: Optional[str] = None):
        self.session_id = session_id or str(uuid.uuid4())
        self.llm_manager = get_llm_manager()
        self.memory = get_memory(self.session_id)
        self.prompt_manager = get_prompt_manager()
        self.chain_manager = get_chain_manager()
        self.tool_registry = get_tool_registry()
        self.cache_manager = get_cache_manager()

        # 设置链式调用的LLM
        if self.llm_manager.is_configured():
            self.chain_manager.set_llm(self.llm_manager.get_llm())
    
    def configure_llm(self, api_key: str, base_url: str, model: str, **kwargs) -> bool:
        """配置语言模型"""
        try:
            self.llm_manager.configure(api_key, base_url, model, **kwargs)
            self.chain_manager.set_llm(self.llm_manager.get_llm())
            return True
        except Exception as e:
            logger.error(f"配置LLM失败: {str(e)}")
            return False
    
    @monitor_performance("process_message")
    async def process_message(self, message: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """处理用户消息"""
        if not self.llm_manager.is_configured():
            return {"success": False, "error": "LLM未配置"}
        
        try:
            # 保存用户消息到记忆
            self.memory.add_user_message(message)
            
            # 获取上下文数据
            context_data = self._gather_context_data(user_id)
            
            # 执行凭证生成链
            chain_inputs = {
                "user_input": message,
                "subjects": context_data.get("subjects", []),
                "assets": context_data.get("assets", []),
                "staff": context_data.get("staff", []),
                "experience": context_data.get("experience", []),
                "user_id": user_id
            }
            
            result = await self.chain_manager.run_chain("voucher_generation", chain_inputs)
            
            if not result.success:
                return {"success": False, "error": result.error}
            
            # 保存AI回复到记忆
            ai_response = json.dumps(result.data, ensure_ascii=False)
            self.memory.add_ai_message(ai_response)
            
            return {
                "success": True,
                "data": result.data,
                "session_id": self.session_id
            }
        except Exception as e:
            logger.error(f"处理消息失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    @monitor_performance("stream_message")
    async def stream_message(self, message: str, user_id: Optional[str] = None) -> AsyncGenerator[str, None]:
        """流式处理用户消息"""
        if not self.llm_manager.is_configured():
            yield json.dumps({"success": False, "error": "LLM未配置"})
            return
        
        try:
            # 保存用户消息到记忆
            self.memory.add_user_message(message)
            
            # 获取上下文数据
            context_data = self._gather_context_data(user_id)
            
            # 准备系统消息和用户消息
            system_prompt = self.prompt_manager.build_prompt("system_base", user_id=user_id)
            user_prompt = self.prompt_manager.build_prompt(
                "voucher_generation",
                user_input=message,
                subjects=json.dumps(context_data.get("subjects", []), ensure_ascii=False),
                assets=json.dumps(context_data.get("assets", []), ensure_ascii=False),
                staff=json.dumps(context_data.get("staff", []), ensure_ascii=False),
                experience=json.dumps(context_data.get("experience", []), ensure_ascii=False),
                user_id=user_id
            )
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            # 流式生成
            full_response = ""
            async for token in self.llm_manager.stream(messages):
                full_response += token
                yield token
            
            # 保存完整回复到记忆
            self.memory.add_ai_message(full_response)
            
        except Exception as e:
            logger.error(f"流式处理消息失败: {str(e)}")
            error_msg = json.dumps({"success": False, "error": str(e)})
            yield error_msg
    
    async def process_document(self, document_type: str, document_content: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """处理文档"""
        if not self.llm_manager.is_configured():
            return {"success": False, "error": "LLM未配置"}
        
        try:
            # 执行文档分析链
            chain_inputs = {
                "document_type": document_type,
                "document_content": document_content,
                "user_id": user_id
            }
            
            result = await self.chain_manager.run_chain("document_analysis", chain_inputs)
            
            if not result.success:
                return {"success": False, "error": result.error}
            
            # 保存到记忆
            self.memory.add_user_message(f"[文档] {document_type}")
            self.memory.add_ai_message(result.data)
            
            return {
                "success": True,
                "data": result.data,
                "session_id": self.session_id
            }
        except Exception as e:
            logger.error(f"处理文档失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def stream_document(self, document_type: str, document_content: str, user_id: Optional[str] = None) -> AsyncGenerator[str, None]:
        """流式处理文档"""
        if not self.llm_manager.is_configured():
            yield json.dumps({"success": False, "error": "LLM未配置"})
            return
        
        try:
            # 保存到记忆
            self.memory.add_user_message(f"[文档] {document_type}")
            
            # 准备系统消息和用户消息
            system_prompt = self.prompt_manager.build_prompt("system_base", user_id=user_id)
            document_prompt = self.prompt_manager.build_prompt(
                "document_analysis",
                document_type=document_type,
                document_content=document_content,
                user_id=user_id
            )
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=document_prompt)
            ]
            
            # 流式生成
            full_response = ""
            async for token in self.llm_manager.stream(messages):
                full_response += token
                yield token
            
            # 保存完整回复到记忆
            self.memory.add_ai_message(full_response)
            
        except Exception as e:
            logger.error(f"流式处理文档失败: {str(e)}")
            error_msg = json.dumps({"success": False, "error": str(e)})
            yield error_msg
    
    async def execute_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """执行工具"""
        try:
            result = await self.tool_registry.execute_tool(tool_name, **kwargs)
            return {
                "success": result.success,
                "data": result.data,
                "error": result.error,
                "execution_time": result.execution_time,
                "tool_name": result.tool_name
            }
        except Exception as e:
            logger.error(f"执行工具失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def get_conversation_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        messages = self.memory.chat_history.get_messages()
        history = []
        
        for msg in messages:
            if isinstance(msg, HumanMessage):
                history.append({"role": "user", "content": msg.content})
            elif isinstance(msg, AIMessage):
                history.append({"role": "assistant", "content": msg.content})
            elif isinstance(msg, SystemMessage):
                history.append({"role": "system", "content": msg.content})
        
        return history
    
    def clear_history(self) -> None:
        """清空对话历史"""
        self.memory.clear()
    
    def _gather_context_data(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """收集上下文数据"""
        # 这里可以从数据库或其他来源获取数据
        # 目前使用内存数据作为示例
        from api.subject import subject_accounts
        from api.asset import assets
        from api.role_staff import staffs
        from api.experience import get_experience
        
        experience = get_experience(user_id, limit=5) if user_id else []
        
        return {
            "subjects": [s.dict() for s in subject_accounts],
            "assets": [a.dict() for a in assets],
            "staff": [s.dict() for s in staffs],
            "experience": experience
        }


# 全局智能体执行器实例字典
_agent_executors: Dict[str, AgentExecutor] = {}

def get_agent_executor(session_id: Optional[str] = None) -> AgentExecutor:
    """获取智能体执行器实例"""
    global _agent_executors
    
    if not session_id:
        session_id = str(uuid.uuid4())
    
    if session_id not in _agent_executors:
        _agent_executors[session_id] = AgentExecutor(session_id)
    
    return _agent_executors[session_id]
