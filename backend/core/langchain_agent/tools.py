"""
工具系统 - 管理和执行各种工具
"""

import logging
from typing import Dict, Any, List, Optional, Callable, Type
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
import asyncio
import json
from datetime import datetime

logger = logging.getLogger(__name__)


class ToolResult(BaseModel):
    """工具执行结果"""
    success: bool
    data: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    tool_name: str = ""


class BaseAgentTool(BaseTool):
    """基础智能体工具"""
    
    def _run(self, *args, **kwargs) -> ToolResult:
        """同步执行（默认抛出异常，强制使用异步）"""
        raise NotImplementedError("请使用异步版本 _arun")
    
    async def _arun(self, *args, **kwargs) -> ToolResult:
        """异步执行"""
        start_time = asyncio.get_event_loop().time()
        try:
            result = await self.execute(*args, **kwargs)
            execution_time = asyncio.get_event_loop().time() - start_time
            return ToolResult(
                success=True,
                data=result,
                execution_time=execution_time,
                tool_name=self.name
            )
        except Exception as e:
            execution_time = asyncio.get_event_loop().time() - start_time
            logger.error(f"工具 {self.name} 执行失败: {str(e)}")
            return ToolResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                tool_name=self.name
            )
    
    async def execute(self, *args, **kwargs) -> Any:
        """具体的执行逻辑，由子类实现"""
        raise NotImplementedError("子类必须实现 execute 方法")


class PDFProcessingTool(BaseAgentTool):
    """PDF处理工具"""
    
    name = "pdf_processor"
    description = "处理PDF文档，提取文本内容"
    
    class InputSchema(BaseModel):
        file_path: str = Field(description="PDF文件路径")
    
    args_schema: Type[BaseModel] = InputSchema
    
    async def execute(self, file_path: str) -> Dict[str, Any]:
        """执行PDF处理"""
        from core.pdf_utils import get_pdf_parser
        
        pdf_parser = get_pdf_parser()
        text_content = pdf_parser.extract_text(file_path)
        
        return {
            "text": text_content,
            "file_path": file_path,
            "processed_at": datetime.now().isoformat()
        }


class OCRProcessingTool(BaseAgentTool):
    """OCR处理工具"""
    
    name = "ocr_processor"
    description = "处理图片文件，提取文本内容"
    
    class InputSchema(BaseModel):
        file_path: str = Field(description="图片文件路径")
    
    args_schema: Type[BaseModel] = InputSchema
    
    async def execute(self, file_path: str) -> Dict[str, Any]:
        """执行OCR处理"""
        from core.ocr_utils import get_ocr_processor
        
        ocr_processor = get_ocr_processor()
        result = ocr_processor.extract_text(file_path)
        
        return result


class DatabaseQueryTool(BaseAgentTool):
    """数据库查询工具"""
    
    name = "database_query"
    description = "查询数据库中的科目、资产、员工等信息"
    
    class InputSchema(BaseModel):
        table: str = Field(description="表名 (subjects/assets/staff)")
        filters: Dict[str, Any] = Field(default={}, description="查询条件")
        limit: int = Field(default=100, description="返回数量限制")
    
    args_schema: Type[BaseModel] = InputSchema
    
    async def execute(self, table: str, filters: Dict[str, Any] = None, limit: int = 100) -> Dict[str, Any]:
        """执行数据库查询"""
        if filters is None:
            filters = {}
        
        # 这里应该连接实际的数据库
        # 目前使用内存数据作为示例
        if table == "subjects":
            from api.subject import subject_accounts
            data = [s.dict() for s in subject_accounts]
        elif table == "assets":
            from api.asset import assets
            data = [a.dict() for a in assets]
        elif table == "staff":
            from api.role_staff import staffs
            data = [s.dict() for s in staffs]
        else:
            raise ValueError(f"不支持的表名: {table}")
        
        # 应用过滤条件
        if filters:
            filtered_data = []
            for item in data:
                match = True
                for key, value in filters.items():
                    if key not in item or item[key] != value:
                        match = False
                        break
                if match:
                    filtered_data.append(item)
            data = filtered_data
        
        # 应用限制
        data = data[:limit]
        
        return {
            "table": table,
            "data": data,
            "count": len(data),
            "filters": filters
        }


class VoucherCreationTool(BaseAgentTool):
    """凭证创建工具"""
    
    name = "voucher_creator"
    description = "创建会计凭证"
    
    class InputSchema(BaseModel):
        voucher_data: Dict[str, Any] = Field(description="凭证数据")
    
    args_schema: Type[BaseModel] = InputSchema
    
    async def execute(self, voucher_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行凭证创建"""
        # 验证凭证数据
        required_fields = ["凭证号", "日期", "摘要", "借方科目", "借方金额", "贷方科目", "贷方金额"]
        for field in required_fields:
            if field not in voucher_data:
                raise ValueError(f"缺少必需字段: {field}")
        
        # 验证借贷平衡
        if voucher_data["借方金额"] != voucher_data["贷方金额"]:
            raise ValueError("借贷不平衡")
        
        # 这里应该保存到数据库
        # 目前只是返回确认信息
        return {
            "success": True,
            "voucher_id": voucher_data.get("凭证号"),
            "message": "凭证创建成功",
            "data": voucher_data
        }


class WebSearchTool(BaseAgentTool):
    """网络搜索工具"""

    name = "web_search"
    description = "搜索网络信息"

    class InputSchema(BaseModel):
        query: str = Field(description="搜索查询")
        max_results: int = Field(default=5, description="最大结果数")

    args_schema: Type[BaseModel] = InputSchema

    async def execute(self, query: str, max_results: int = 5) -> Dict[str, Any]:
        """执行网络搜索"""
        try:
            # 使用DuckDuckGo搜索
            try:
                from duckduckgo_search import DDGS

                with DDGS() as ddgs:
                    results = []
                    for r in ddgs.text(query, max_results=max_results):
                        results.append({
                            "title": r.get("title", ""),
                            "url": r.get("href", ""),
                            "snippet": r.get("body", "")
                        })

                    return {
                        "query": query,
                        "results": results,
                        "count": len(results)
                    }
            except ImportError:
                # 如果没有安装duckduckgo_search，返回模拟结果
                logger.warning("duckduckgo_search未安装，返回模拟搜索结果")
                results = [
                    {
                        "title": f"搜索结果 {i+1}",
                        "url": f"https://example.com/result{i+1}",
                        "snippet": f"关于 '{query}' 的搜索结果 {i+1}"
                    }
                    for i in range(min(max_results, 3))
                ]

                return {
                    "query": query,
                    "results": results,
                    "count": len(results)
                }
        except Exception as e:
            raise Exception(f"网络搜索失败: {str(e)}")


class ToolRegistry:
    """工具注册表"""
    
    def __init__(self):
        self._tools: Dict[str, BaseAgentTool] = {}
        self._initialize_default_tools()
    
    def _initialize_default_tools(self):
        """初始化默认工具"""
        default_tools = [
            PDFProcessingTool(),
            OCRProcessingTool(),
            DatabaseQueryTool(),
            VoucherCreationTool(),
            WebSearchTool()
        ]

        for tool in default_tools:
            self.register_tool(tool)

        # 注册外部工具
        try:
            from .external_tools import register_external_tools
            register_external_tools()
        except ImportError as e:
            logger.warning(f"无法导入外部工具: {e}")
    
    def register_tool(self, tool: BaseAgentTool) -> None:
        """注册工具"""
        self._tools[tool.name] = tool
        logger.info(f"注册工具: {tool.name}")
    
    def get_tool(self, name: str) -> Optional[BaseAgentTool]:
        """获取工具"""
        return self._tools.get(name)
    
    def get_all_tools(self) -> List[BaseAgentTool]:
        """获取所有工具"""
        return list(self._tools.values())
    
    def list_tools(self) -> List[str]:
        """列出所有工具名称"""
        return list(self._tools.keys())
    
    def remove_tool(self, name: str) -> bool:
        """移除工具"""
        if name in self._tools:
            del self._tools[name]
            logger.info(f"移除工具: {name}")
            return True
        return False
    
    async def execute_tool(self, name: str, **kwargs) -> ToolResult:
        """执行工具"""
        tool = self.get_tool(name)
        if not tool:
            return ToolResult(
                success=False,
                error=f"工具不存在: {name}",
                tool_name=name
            )
        
        return await tool._arun(**kwargs)


# 全局工具注册表实例
_tool_registry = None

def get_tool_registry() -> ToolRegistry:
    """获取全局工具注册表实例"""
    global _tool_registry
    if _tool_registry is None:
        _tool_registry = ToolRegistry()
    return _tool_registry

def register_tool(tool: BaseAgentTool) -> None:
    """注册工具"""
    get_tool_registry().register_tool(tool)

def get_tools() -> List[BaseAgentTool]:
    """获取所有工具"""
    return get_tool_registry().get_all_tools()
