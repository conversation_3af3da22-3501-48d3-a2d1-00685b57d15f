from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from datetime import datetime

class ExperienceRecord(BaseModel):
    user_id: str
    type: str  # 'voucher', 'subject', 'asset', 'staff' 等
    original: Dict[str, Any]
    modified: Dict[str, Any]
    timestamp: str
    remark: str = ""

# 内存存储经验知识
experience_records: List[ExperienceRecord] = []

# 添加经验记录
def add_experience(record: ExperienceRecord):
    experience_records.append(record)

# 查询某用户的经验记录（可按类型筛选，默认返回全部）
def get_experience(user_id: str, type: Optional[str] = None, limit: int = 10) -> List[Dict[str, Any]]:
    filtered = [r for r in experience_records if r.user_id == user_id]
    if type:
        filtered = [r for r in filtered if r.type == type]
    # 按时间倒序，取最近 limit 条
    filtered.sort(key=lambda r: r.timestamp, reverse=True)
    return [r.dict() for r in filtered[:limit]] 